#include "app_resampler.h"
#include "logger.h"
#include <string.h>
#include <stdio.h>

/**
 * 初始化应用重采样器
 */
int app_resampler_wrapper_init(AppResamplerWrapper *wrapper,
                              int input_rate, int output_rate,
                              int channels, int quality) {
    if (!wrapper) {
        LOG_INFO(MODULE_APP_RESAMPLER, "Error: Invalid wrapper for resampler init");
        return -1;
    }
    
    // 参数验证
    if (input_rate <= 0 || output_rate <= 0 || channels <= 0) {
        LOG_INFO(MODULE_APP_RESAMPLER, "Error: Invalid resampler parameters");
        return -1;
    }
    
    if (quality < 0 || quality > 10) {
        LOG_INFO(MODULE_APP_RESAMPLER, "Warning: Quality should be 0-10, using default 8");
        quality = 8;
    }
    
    // 初始化结构体
    memset(wrapper, 0, sizeof(AppResamplerWrapper));
    wrapper->input_rate = input_rate;
    wrapper->output_rate = output_rate;
    wrapper->channels = channels;
    wrapper->quality = quality;
    
    // 创建SpeexDSP重采样器
    int err = 0;
    wrapper->resampler = speex_resampler_init(channels, input_rate, output_rate, quality, &err);

    if (!wrapper->resampler || err != RESAMPLER_ERR_SUCCESS) {
        LOG_INFO(MODULE_APP_RESAMPLER, "❌ Error: Failed to initialize Speex resampler: %d", err);
        LOG_INFO(MODULE_APP_RESAMPLER, "   Parameters: channels=%d, input_rate=%d, output_rate=%d, quality=%d",
               channels, input_rate, output_rate, quality);
        return -1;
    }

    // 验证重采样器状态
    LOG_INFO(MODULE_APP_RESAMPLER, "✅ SpeexDSP resampler created successfully");

    // 重采样器创建成功，无需测试

    wrapper->initialized = true;
    
    LOG_INFO(MODULE_APP_RESAMPLER, "SpeexDSP Resampler initialized:");
    LOG_INFO(MODULE_APP_RESAMPLER, "  - Input rate: %dHz", input_rate);
    LOG_INFO(MODULE_APP_RESAMPLER, "  - Output rate: %dHz", output_rate);
    LOG_INFO(MODULE_APP_RESAMPLER, "  - Channels: %d", channels);
    LOG_INFO(MODULE_APP_RESAMPLER, "  - Quality: %d/10", quality);
    LOG_INFO(MODULE_APP_RESAMPLER, "  - Ratio: %.3f", (float)output_rate / input_rate);
    
    return 0;
}

/**
 * 执行重采样
 */
int app_resampler_wrapper_process(AppResamplerWrapper *wrapper,
                                 const int16_t *input, int input_len,
                                 int16_t *output, int output_len,
                                 int *actual_output_len) {
    if (!wrapper || !wrapper->initialized) {
        LOG_INFO(MODULE_APP_RESAMPLER, "Error: Resampler not initialized");
        return -1;
    }
    
    if (!input || !output || !actual_output_len) {
        LOG_INFO(MODULE_APP_RESAMPLER, "Error: Invalid parameters for resampler process");
        return -1;
    }
    
    // 验证输入参数
    if (input_len <= 0 || output_len <= 0) {
        LOG_INFO(MODULE_APP_RESAMPLER, "Error: Invalid input/output length: input=%d, output=%d", input_len, output_len);
        return -1;
    }

    //LOG_INFO(MODULE_APP_RESAMPLER, "🔍 Resampler process: input_len=%d frames, output_len=%d frames, channels=%d",
    //       input_len, output_len, wrapper->channels);

    // 对于交错格式：
    // input_len 和 output_len 是每声道的样本数（帧数）
    // 但 input 和 output 缓冲区包含所有声道的交错数据
    spx_uint32_t in_len = (spx_uint32_t)input_len;
    spx_uint32_t out_len = (spx_uint32_t)output_len;

    //// 打印输入数据的前几个样本用于调试
    //LOG_INFO(MODULE_APP_RESAMPLER, "🔍 Input samples before resampling: [%d,%d,%d,%d,%d,%d,%d,%d]",
    //       input[0], input[1], input[2], input[3],
    //       input[4], input[5], input[6], input[7]);

    // 验证输入数据不全为0
    bool has_non_zero = false;
    for (int i = 0; i < 8 && i < input_len * wrapper->channels; i++) {
        if (input[i] != 0) {
            has_non_zero = true;
            break;
        }
    }
    if (!has_non_zero) {
        LOG_INFO(MODULE_APP_RESAMPLER, "⚠️ Warning: Input data appears to be all zeros");
    }

    // 尝试使用非交错格式重采样（更可靠）
    if (wrapper->channels == 1) {
        // 单声道，直接使用非交错格式
        int err = speex_resampler_process_int(wrapper->resampler, 0,
                                             input, &in_len,
                                             output, &out_len);
        if (err != RESAMPLER_ERR_SUCCESS) {
            LOG_INFO(MODULE_APP_RESAMPLER, "❌ Error: Mono resampler failed: %d", err);
            return -1;
        }
    } else {
        // 多声道，使用交错格式
        int err = speex_resampler_process_interleaved_int(wrapper->resampler,
                                                         input, &in_len,
                                                         output, &out_len);
        if (err != RESAMPLER_ERR_SUCCESS) {
            LOG_INFO(MODULE_APP_RESAMPLER, "❌ Error: Interleaved resampler failed: %d", err);
            return -1;
        }
    }

    //// 打印输出数据的前几个样本用于调试
    //LOG_INFO(MODULE_APP_RESAMPLER, "🔍 Output samples after resampling: [%d,%d,%d,%d,%d,%d,%d,%d]",
    //       output[0], output[1], output[2], output[3],
    //       output[4], output[5], output[6], output[7]);
    //
    //LOG_INFO(MODULE_APP_RESAMPLER, "🔍 Resampler result: processed %d→%d frames per channel",
    //       (int)in_len, (int)out_len);

    // 验证输出数据
    bool output_has_non_zero = false;
    for (int i = 0; i < 8 && i < (int)out_len * wrapper->channels; i++) {
        if (output[i] != 0) {
            output_has_non_zero = true;
            break;
        }
    }
    if (!output_has_non_zero) {
        LOGD(MODULE_APP_RESAMPLER, "⚠️ Warning: Output data is all zeros - resampler may have failed");
    }

    *actual_output_len = (int)out_len;
    
    return 0;
}

/**
 * 清理重采样器
 */
void app_resampler_wrapper_cleanup(AppResamplerWrapper *wrapper) {
    if (!wrapper) return;
    
    if (wrapper->resampler) {
        speex_resampler_destroy(wrapper->resampler);
        wrapper->resampler = NULL;
    }
    
    wrapper->initialized = false;
    LOGD(MODULE_APP_RESAMPLER, "SpeexDSP Resampler cleaned up");
}

/**
 * 计算输出样本数
 */
int app_resampler_wrapper_get_output_length(const AppResamplerWrapper *wrapper,
                                           int input_len) {
    if (!wrapper || !wrapper->initialized) {
        return -1;
    }
    
    // 计算预期输出长度
    return (int)((long long)input_len * wrapper->output_rate / wrapper->input_rate);
}

/**
 * 重置重采样器状态
 */
int app_resampler_wrapper_reset(AppResamplerWrapper *wrapper) {
    if (!wrapper || !wrapper->initialized) {
        LOG_INFO(MODULE_APP_RESAMPLER, "Error: Resampler not initialized for reset");
        return -1;
    }
    
    int err = speex_resampler_reset_mem(wrapper->resampler);
    if (err != RESAMPLER_ERR_SUCCESS) {
        LOG_INFO(MODULE_APP_RESAMPLER, "Error: Failed to reset resampler: %d", err);
        return -1;
    }
    
    return 0;
}

/**
 * 获取重采样器延迟
 */
int app_resampler_wrapper_get_delay(const AppResamplerWrapper *wrapper) {
    if (!wrapper || !wrapper->initialized) {
        return 0;
    }
    
    // SpeexDSP重采样器的延迟通常很小
    return speex_resampler_get_input_latency(wrapper->resampler);
}

/**
 * 简化的重采样函数（用于替换现有的简单重采样）
 */
int app_resample_audio(const int16_t *input, int input_samples, int input_rate, int input_channels,
                      int16_t *output, int output_buffer_size, int output_rate, int output_channels,
                      int *output_samples) {
    if (!input || !output || !output_samples) {
        LOG_INFO(MODULE_APP_RESAMPLER, "❌ Error: Invalid parameters for app_resample_audio");
        return -1;
    }

    // 计算每声道的样本数
    int input_frames = input_samples / input_channels;
    int max_output_frames = output_buffer_size / output_channels;

    // 如果格式相同，直接复制
    if (input_rate == output_rate && input_channels == output_channels) {
        int copy_samples = (input_samples < output_buffer_size) ? input_samples : output_buffer_size;
        memcpy(output, input, copy_samples * sizeof(int16_t));
        *output_samples = copy_samples;

        return 0;
    }

    // 创建临时重采样器
    AppResamplerWrapper wrapper;
    if (app_resampler_wrapper_init(&wrapper, input_rate, output_rate, input_channels, 8) != 0) {
        LOG_INFO(MODULE_APP_RESAMPLER, "❌ Error: Failed to initialize resampler");
        return -1;
    }
    
    // 步骤1：重采样（保持原声道数）
    int expected_output_frames = app_resampler_wrapper_get_output_length(&wrapper, input_frames);
    int temp_total_samples = expected_output_frames * input_channels;
    int16_t *temp_buffer = malloc(temp_total_samples * sizeof(int16_t));

    if (!temp_buffer) {
        LOG_INFO(MODULE_APP_RESAMPLER, "❌ Error: Failed to allocate temp buffer for %d samples", temp_total_samples);
        app_resampler_wrapper_cleanup(&wrapper);
        return -1;
    }

    // 初始化缓冲区
    memset(temp_buffer, 0, temp_total_samples * sizeof(int16_t));
    LOGD(MODULE_APP_RESAMPLER, "🔍 Allocated temp buffer: %d samples (%d frames × %d channels)",
         temp_total_samples, expected_output_frames, input_channels);

    int actual_output_frames;
    if (app_resampler_wrapper_process(&wrapper, input, input_frames,
                                     temp_buffer, expected_output_frames, &actual_output_frames) != 0) {
        LOG_INFO(MODULE_APP_RESAMPLER, "❌ Error: Resampling failed");
        free(temp_buffer);
        app_resampler_wrapper_cleanup(&wrapper);
        return -1;
    }

    LOGD(MODULE_APP_RESAMPLER, "✅ Resampling successful: %d frames → %d frames (channels: %d)",
         input_frames, actual_output_frames, input_channels);

    // 步骤2：声道转换（如果需要）
    if (input_channels != output_channels) {
        
        int final_frames = (actual_output_frames < max_output_frames) ? actual_output_frames : max_output_frames;

        if (input_channels == 2 && output_channels == 1) {
            // 立体声转单声道
            LOGD(MODULE_APP_RESAMPLER, "🔍 Converting stereo to mono: %d frames", final_frames);

            // 调试：打印temp_buffer的前几个样本
            LOGD(MODULE_APP_RESAMPLER, "🔍 Resampled stereo buffer first 8 samples: [%d,%d,%d,%d,%d,%d,%d,%d]",
                 temp_buffer[0], temp_buffer[1], temp_buffer[2], temp_buffer[3],
                 temp_buffer[4], temp_buffer[5], temp_buffer[6], temp_buffer[7]);

            for (int i = 0; i < final_frames; i++) {
                // 立体声混合为单声道
                int32_t left = temp_buffer[i * 2];
                int32_t right = temp_buffer[i * 2 + 1];
                int32_t mixed = (left + right) / 2;

                // 限制范围
                if (mixed > 32767) mixed = 32767;
                if (mixed < -32768) mixed = -32768;

                output[i] = (int16_t)mixed;
            }

            // 调试：打印转换后的前几个样本
            LOGD(MODULE_APP_RESAMPLER, "🔍 Mono output first 8 samples: [%d,%d,%d,%d,%d,%d,%d,%d]",
                 output[0], output[1], output[2], output[3],
                 output[4], output[5], output[6], output[7]);

            *output_samples = final_frames;
        } else if (input_channels == 1 && output_channels == 2) {
            // 单声道转立体声
            LOG_INFO(MODULE_APP_RESAMPLER, "🔍 Converting mono to stereo: %d frames", final_frames);
            for (int i = 0; i < final_frames; i++) {
                output[i * 2] = temp_buffer[i];
                output[i * 2 + 1] = temp_buffer[i];
            }
            *output_samples = final_frames * 2;
        } else {
            // 其他情况，直接复制
            LOG_INFO(MODULE_APP_RESAMPLER, "🔍 Direct copy after resampling: %d frames", final_frames);
            int copy_samples = final_frames * input_channels;
            memcpy(output, temp_buffer, copy_samples * sizeof(int16_t));
            *output_samples = copy_samples;
        }
    } else {
        // 声道数相同，直接使用重采样结果
        LOG_INFO(MODULE_APP_RESAMPLER, "🔍 Same channel count, using resampled data directly");
        int copy_samples = (actual_output_frames * input_channels < output_buffer_size) ?
                          actual_output_frames * input_channels : output_buffer_size;
        memcpy(output, temp_buffer, copy_samples * sizeof(int16_t));
        *output_samples = copy_samples;
    }

    free(temp_buffer);
    app_resampler_wrapper_cleanup(&wrapper);

    // 最终验证
    LOG_INFO(MODULE_APP_RESAMPLER, "✅ Audio resampling completed: %d → %d samples", input_samples, *output_samples);

    // 验证输出数据不全为0
    bool has_valid_output = false;
    for (int i = 0; i < 8 && i < *output_samples; i++) {
        if (output[i] != 0) {
            has_valid_output = true;
            break;
        }
    }

    if (!has_valid_output) {
        LOG_INFO(MODULE_APP_RESAMPLER, "⚠️ Warning: Output audio appears to be silent");
    } else {
        LOG_INFO(MODULE_APP_RESAMPLER, "✅ Output audio contains valid data");
    }

    return 0;
}

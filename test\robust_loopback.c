#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <alsa/asoundlib.h>
#include <signal.h>
#include <unistd.h>

// 全局变量
static volatile int g_running = 1;

// 信号处理函数
void signal_handler(int sig) {
    printf("\nReceived signal %d, stopping...\n", sig);
    g_running = 0;
}

// 打印错误信息
void print_error(const char *func, int err) {
    fprintf(stderr, "ALSA error in %s: %s\n", func, snd_strerror(err));
}

// 测试设备是否可用
int test_device(const char *device_name, snd_pcm_stream_t stream) {
    snd_pcm_t *handle;
    int err;
    
    err = snd_pcm_open(&handle, device_name, stream, SND_PCM_NONBLOCK);
    if (err < 0) {
        return 0;  // 设备不可用
    }
    
    snd_pcm_close(handle);
    return 1;  // 设备可用
}

// 获取设备支持的格式
void get_device_info(const char *device_name, snd_pcm_stream_t stream) {
    snd_pcm_t *handle;
    snd_pcm_hw_params_t *params;
    int err;
    
    printf("=== Device Info: %s (%s) ===\n", device_name, 
           stream == SND_PCM_STREAM_CAPTURE ? "Capture" : "Playback");
    
    if ((err = snd_pcm_open(&handle, device_name, stream, 0)) < 0) {
        print_error("snd_pcm_open", err);
        return;
    }
    
    snd_pcm_hw_params_alloca(&params);
    
    if ((err = snd_pcm_hw_params_any(handle, params)) < 0) {
        print_error("snd_pcm_hw_params_any", err);
        snd_pcm_close(handle);
        return;
    }
    
    // 获取支持的格式
    printf("Supported formats: ");
    snd_pcm_format_t formats[] = {
        SND_PCM_FORMAT_S16_LE, SND_PCM_FORMAT_S24_LE, 
        SND_PCM_FORMAT_S32_LE, SND_PCM_FORMAT_FLOAT_LE
    };
    
    for (int i = 0; i < 4; i++) {
        if (snd_pcm_hw_params_test_format(handle, params, formats[i]) == 0) {
            printf("%s ", snd_pcm_format_name(formats[i]));
        }
    }
    printf("\n");
    
    // 获取声道数范围
    unsigned int min_channels, max_channels;
    snd_pcm_hw_params_get_channels_min(params, &min_channels);
    snd_pcm_hw_params_get_channels_max(params, &max_channels);
    printf("Channels: %u-%u\n", min_channels, max_channels);
    
    // 获取采样率范围
    unsigned int min_rate, max_rate;
    int dir;
    snd_pcm_hw_params_get_rate_min(params, &min_rate, &dir);
    snd_pcm_hw_params_get_rate_max(params, &max_rate, &dir);
    printf("Sample rates: %u-%u Hz\n", min_rate, max_rate);
    
    snd_pcm_close(handle);
    printf("========================\n");
}

// 尝试多种设备配置
int configure_capture_device(snd_pcm_t *handle, unsigned int *rate, unsigned int *channels, snd_pcm_format_t *format) {
    int err;
    
    // 尝试的配置组合
    struct {
        snd_pcm_format_t format;
        unsigned int rate;
        unsigned int channels;
    } configs[] = {
        {SND_PCM_FORMAT_S32_LE, 96000, 2},
        {SND_PCM_FORMAT_S32_LE, 48000, 2},
        {SND_PCM_FORMAT_S16_LE, 48000, 2},
        {SND_PCM_FORMAT_S16_LE, 44100, 2},
        {SND_PCM_FORMAT_S16_LE, 16000, 2},
        {SND_PCM_FORMAT_S32_LE, 96000, 1},
        {SND_PCM_FORMAT_S16_LE, 48000, 1},
    };
    
    for (int i = 0; i < 7; i++) {
        printf("Trying capture config: %s, %uHz, %uch... ", 
               snd_pcm_format_name(configs[i].format), 
               configs[i].rate, configs[i].channels);
        
        err = snd_pcm_set_params(handle,
                                configs[i].format,
                                SND_PCM_ACCESS_RW_INTERLEAVED,
                                configs[i].channels,
                                configs[i].rate,
                                1,  // 允许重采样
                                500000);  // 500ms延迟
        
        if (err >= 0) {
            *format = configs[i].format;
            *rate = configs[i].rate;
            *channels = configs[i].channels;
            printf("SUCCESS\n");
            return 0;
        } else {
            printf("FAILED (%s)\n", snd_strerror(err));
        }
    }
    
    return -1;
}

// 主函数
int main(int argc, char *argv[]) {
    int err;
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    printf("=== Robust ALSA Audio Loopback ===\n");
    
    // 测试多个可能的设备
    const char *capture_devices[] = {"hw:2,0", "hw:2", "hw:1,0", "hw:1", "hw:0,0", "hw:0", "default"};
    const char *playback_devices[] = {"default", "hw:0,0", "hw:0", "hw:1,0", "hw:1"};
    
    const char *capture_device = NULL;
    const char *playback_device = NULL;
    
    // 查找可用的录音设备
    printf("Testing capture devices...\n");
    for (int i = 0; i < 7; i++) {
        printf("Testing %s... ", capture_devices[i]);
        if (test_device(capture_devices[i], SND_PCM_STREAM_CAPTURE)) {
            printf("OK\n");
            capture_device = capture_devices[i];
            break;
        } else {
            printf("FAILED\n");
        }
    }
    
    if (!capture_device) {
        printf("No working capture device found!\n");
        return 1;
    }
    
    // 查找可用的播放设备
    printf("Testing playback devices...\n");
    for (int i = 0; i < 5; i++) {
        printf("Testing %s... ", playback_devices[i]);
        if (test_device(playback_devices[i], SND_PCM_STREAM_PLAYBACK)) {
            printf("OK\n");
            playback_device = playback_devices[i];
            break;
        } else {
            printf("FAILED\n");
        }
    }
    
    if (!playback_device) {
        printf("No working playback device found!\n");
        return 1;
    }
    
    printf("Using capture device: %s\n", capture_device);
    printf("Using playback device: %s\n", playback_device);
    
    // 获取设备信息
    get_device_info(capture_device, SND_PCM_STREAM_CAPTURE);
    get_device_info(playback_device, SND_PCM_STREAM_PLAYBACK);
    
    // 打开设备
    snd_pcm_t *capture_handle, *playback_handle;
    
    if ((err = snd_pcm_open(&capture_handle, capture_device, SND_PCM_STREAM_CAPTURE, 0)) < 0) {
        print_error("snd_pcm_open capture", err);
        return 1;
    }
    
    if ((err = snd_pcm_open(&playback_handle, playback_device, SND_PCM_STREAM_PLAYBACK, 0)) < 0) {
        print_error("snd_pcm_open playback", err);
        snd_pcm_close(capture_handle);
        return 1;
    }
    
    // 配置录音设备
    unsigned int capture_rate, capture_channels;
    snd_pcm_format_t capture_format;
    
    if (configure_capture_device(capture_handle, &capture_rate, &capture_channels, &capture_format) < 0) {
        printf("Failed to configure capture device\n");
        goto cleanup;
    }
    
    printf("Capture configured: %s, %uch, %uHz\n", 
           snd_pcm_format_name(capture_format), capture_channels, capture_rate);
    
    // 配置播放设备 - 使用简单的16位立体声
    if ((err = snd_pcm_set_params(playback_handle,
                                  SND_PCM_FORMAT_S16_LE,
                                  SND_PCM_ACCESS_RW_INTERLEAVED,
                                  2,  // 立体声
                                  48000,  // 48kHz
                                  1,  // 允许重采样
                                  500000)) < 0) {  // 500ms延迟
        print_error("snd_pcm_set_params playback", err);
        goto cleanup;
    }
    
    printf("Playback configured: S16_LE, 2ch, 48000Hz\n");
    
    // 计算参数
    int sample_size = snd_pcm_format_width(capture_format) / 8;
    snd_pcm_uframes_t frames_per_period = 1024;
    
    // 分配缓冲区
    void *capture_buffer = malloc(frames_per_period * capture_channels * sample_size);
    int16_t *playback_buffer = malloc(frames_per_period * 2 * sizeof(int16_t));
    
    if (!capture_buffer || !playback_buffer) {
        printf("Failed to allocate buffers\n");
        goto cleanup;
    }
    
    printf("Starting audio loopback...\n");
    printf("Press Ctrl+C to stop\n");
    
    // 主循环
    int frame_count = 0;
    while (g_running) {
        // 读取数据
        snd_pcm_sframes_t frames_read = snd_pcm_readi(capture_handle, capture_buffer, frames_per_period);
        
        if (frames_read == -EPIPE) {
            printf("Capture underrun, recovering...\n");
            snd_pcm_prepare(capture_handle);
            continue;
        } else if (frames_read < 0) {
            print_error("snd_pcm_readi", frames_read);
            if (snd_pcm_recover(capture_handle, frames_read, 0) < 0) {
                break;
            }
            continue;
        }
        
        // 简单的格式转换和复制
        for (snd_pcm_uframes_t i = 0; i < frames_read; i++) {
            int16_t sample = 0;
            
            if (capture_format == SND_PCM_FORMAT_S32_LE) {
                int32_t *buf32 = (int32_t*)capture_buffer;
                sample = (int16_t)(buf32[i * capture_channels] >> 16);
            } else if (capture_format == SND_PCM_FORMAT_S16_LE) {
                int16_t *buf16 = (int16_t*)capture_buffer;
                sample = buf16[i * capture_channels];
            }
            
            // 应用增益
            float gain = 10.0f;
            float sample_float = sample * gain;
            
            if (sample_float > 32767.0f) sample_float = 32767.0f;
            if (sample_float < -32768.0f) sample_float = -32768.0f;
            
            int16_t output_sample = (int16_t)sample_float;
            
            // 复制到立体声输出
            playback_buffer[i * 2] = output_sample;
            playback_buffer[i * 2 + 1] = output_sample;
        }
        
        // 写入数据
        snd_pcm_sframes_t frames_written = snd_pcm_writei(playback_handle, playback_buffer, frames_read);
        
        if (frames_written == -EPIPE) {
            printf("Playback underrun, recovering...\n");
            snd_pcm_prepare(playback_handle);
            continue;
        } else if (frames_written < 0) {
            print_error("snd_pcm_writei", frames_written);
            if (snd_pcm_recover(playback_handle, frames_written, 0) < 0) {
                break;
            }
            continue;
        }
        
        frame_count++;
        if (frame_count % 100 == 0) {
            // 计算音频电平
            int32_t max_input = 0;
            
            if (capture_format == SND_PCM_FORMAT_S32_LE) {
                int32_t *buf32 = (int32_t*)capture_buffer;
                for (snd_pcm_uframes_t i = 0; i < frames_read * capture_channels; i++) {
                    int32_t val = abs(buf32[i]);
                    if (val > max_input) max_input = val;
                }
                max_input >>= 16;  // 转换到16位范围
            } else {
                int16_t *buf16 = (int16_t*)capture_buffer;
                for (snd_pcm_uframes_t i = 0; i < frames_read * capture_channels; i++) {
                    int16_t val = abs(buf16[i]);
                    if (val > max_input) max_input = val;
                }
            }
            
            int16_t max_output = 0;
            for (snd_pcm_uframes_t i = 0; i < frames_read * 2; i++) {
                int16_t val = abs(playback_buffer[i]);
                if (val > max_output) max_output = val;
            }
            
            printf("Frame %d: Read=%ld, Written=%ld, Input_max=%d, Output_max=%d\n", 
                   frame_count, frames_read, frames_written, (int)max_input, max_output);
        }
    }
    
cleanup:
    // 清理
    if (capture_buffer) free(capture_buffer);
    if (playback_buffer) free(playback_buffer);
    
    if (capture_handle) {
        snd_pcm_drop(capture_handle);
        snd_pcm_close(capture_handle);
    }
    
    if (playback_handle) {
        snd_pcm_drop(playback_handle);
        snd_pcm_close(playback_handle);
    }
    
    printf("Audio loopback finished\n");
    return 0;
}

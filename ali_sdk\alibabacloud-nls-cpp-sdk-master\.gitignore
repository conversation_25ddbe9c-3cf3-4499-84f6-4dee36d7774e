.clang-format
core
gmon.out
build/
*.o
*.a
*.so
*.wav
log-*.log
*TaskFailed.log
*.tmp
resource/.DS_Store
nlsCppSdk/vipServerClient/libs
nlsCppSdk/win/.vs/
nlsCppSdk/win/uuid/
nlsCppSdk/win/packages.config
nlsCppSdk/win/packages/
nlsCsharpSdk/nlsCsharpSdk/.vs/
nlsCsharpSdk/nlsCsharpSdk/bin/
nlsCsharpSdk/nlsCsharpSdk/obj/
nlsCsharpSdk/nlsCsharpSdk/x64/
nlsCsharpSdk/nlsCsharpSdk/Properties/
nlsCsharpSdk/nlsCsharpSdk/Win32/
nlsCsharpSdk/nlsCsharpSdk/packages/
nlsCsharpSdk/nlsCsharpSdkDemo/bin/
nlsCsharpSdk/nlsCsharpSdkDemo/obj/
nlsCsharpSdk/nlsCsharpSdkDemo/Properties/bin/
nlsCsharpSdk/nlsCsharpSdkExtern/x64/
nlsCsharpSdk/nlsCsharpSdkExtern/Win32/
nlsCsharpSdk/nlsCsharpSdkExtern/packages.config
.vscode/
ossutil_output/
#ifndef WEBSOCKET_SERVER_H
#define WEBSOCKET_SERVER_H

#include <libwebsockets.h>
#include <stdint.h>
#include <pthread.h>

// WebSocket服务器配置
#define WS_SERVER_PORT 9000
#define MAX_WS_MESSAGE_SIZE 16384  // 增加到16KB以支持音频数据传输

// WebSocket服务器状态
typedef struct {
    struct lws_context *context;
    pthread_t thread;
    int running;
    int port;
} WebSocketServer;

// WebSocket消息类型
typedef enum {
    WS_MSG_AEC_DELAY_GET,      // 获取AEC延时
    WS_MSG_AEC_DELAY_SET,      // 设置AEC延时
    WS_MSG_AEC_DELAY_NOTIFY,   // AEC延时变化通知
    WS_MSG_AUDIO_STATUS,       // 音频状态
    WS_MSG_AUDIO_DATA,         // 音频数据
    WS_MSG_AUDIO_STREAM_CTRL,  // 音频流控制
    WS_MSG_ERROR               // 错误消息
} ws_message_type_t;

// WebSocket消息结构
typedef struct {
    ws_message_type_t type;
    char data[MAX_WS_MESSAGE_SIZE];
} ws_message_t;

// 全局变量声明
extern volatile int g_aec_reference_delay_ms;

// 函数声明
int websocket_server_init(WebSocketServer *server, int port);
void websocket_server_cleanup(WebSocketServer *server);
int websocket_server_start(WebSocketServer *server);
void websocket_server_stop(WebSocketServer *server);

// 广播消息到所有连接的客户端
void websocket_broadcast_aec_delay_change(int old_delay, int new_delay);
void websocket_broadcast_auto_adjust_change(int old_enabled, int new_enabled);
void websocket_broadcast_delay_estimate(float estimated_delay_ms, float mic_rms, float ref_rms,
                                       float correlation, int current_delay_ms);
void websocket_broadcast_signal_strength(float mic_rms, float ref_rms, const char* status);
void websocket_broadcast_audio_status(const char *status);

// 音频数据传输
void websocket_send_audio_data(const uint8_t *pcm_data, size_t data_size,
                              int sample_rate, int channels);
void websocket_broadcast_audio_stream_control(const char *action);

// WebSocket协议回调函数
int callback_aec_control(struct lws *wsi, enum lws_callback_reasons reason,
                        void *user, void *in, size_t len);

// WebSocket线程函数
void *websocket_server_thread(void *arg);

#endif // WEBSOCKET_SERVER_H

#include "speech_upload.h"
#include "app_resampler.h"
#include "ali_asr.h"
#include <curl/curl.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <sys/stat.h>
#include <unistd.h>
#include <ctype.h>
#include <opus/opus.h>
#include <ogg/ogg.h>
#include "logger.h"

// Opus编码参数
#define OPUS_FRAME_SIZE 960        // 每帧样本数（20ms @ 48kHz）
#define OPUS_MAX_PACKET_SIZE 4000  // 最大Opus数据包大小
#define OPUS_BITRATE 64000         // 比特率（64kbps，适合语音）

// 内部函数声明
static int16_t* convert_audio_format_speex(const int16_t *input_data, size_t input_samples,
                                          int input_rate, int input_channels,
                                          int output_rate, int output_channels,
                                          size_t *output_samples);



// 全局变量
static char g_server_url[MAX_URL_SIZE] = DEFAULT_ASR_SERVER_URL;
static char g_tts_server_url[MAX_URL_SIZE] = DEFAULT_TTS_SERVER_URL;
static bool g_curl_initialized = false;

/**
 * 响应数据写入回调函数
 */
static size_t write_response_callback(void *contents, size_t size, size_t nmemb, UploadResponse *response) {
    size_t total_size = size * nmemb;
    
    // 检查是否需要扩展缓冲区
    if (response->size + total_size >= (size_t)response->capacity) {
        size_t new_capacity = (size_t)response->capacity * 2;
        if (new_capacity < response->size + total_size + 1) {
            new_capacity = response->size + total_size + 1;
        }
        
        char *new_data = realloc(response->data, new_capacity);
        if (!new_data) {
            LOG_INFO(MODULE_SPEECH_UPLOAD, "Error: Failed to allocate memory for response");
            return 0;
        }
        
        response->data = new_data;
        response->capacity = new_capacity;
    }
    
    // 复制数据
    memcpy(response->data + response->size, contents, total_size);
    response->size += total_size;
    response->data[response->size] = '\0';  // 确保字符串结尾
    
    return total_size;
}

/**
 * 初始化语音上传模块
 */
int speech_upload_init(void) {
    if (g_curl_initialized) {
        return 0;  // 已经初始化
    }
    
    CURLcode res = curl_global_init(CURL_GLOBAL_DEFAULT);
    if (res != CURLE_OK) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Error: Failed to initialize libcurl: %s", curl_easy_strerror(res));
        return -1;
    }
    
    g_curl_initialized = true;
    LOG_INFO(MODULE_SPEECH_UPLOAD, "Speech upload module initialized");
    LOG_INFO(MODULE_SPEECH_UPLOAD, "Default ASR server: %s", g_server_url);
    LOG_INFO(MODULE_SPEECH_UPLOAD, "Default TTS server: %s", g_tts_server_url);

    return 0;
}

/**
 * 清理语音上传模块
 */
void speech_upload_cleanup(void) {
    if (g_curl_initialized) {
        curl_global_cleanup();
        g_curl_initialized = false;
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Speech upload module cleaned up");
    }
}

/**
 * 创建标准的OpusHead头数据包
 */
static int create_opus_header(unsigned char *header_data, int sample_rate, int channels) {
    memset(header_data, 0, 19);  // 清零所有字段

    // OpusHead标识符（8字节）
    memcpy(header_data, "OpusHead", 8);

    // 版本号（1字节）
    header_data[8] = 1;

    // 声道数（1字节）
    header_data[9] = (unsigned char)channels;

    // 预跳过样本数（16位小端）- 根据采样率调整
    int pre_skip = (sample_rate == 16000) ? 104 : 312;  // 16kHz: 104, 48kHz: 312
    header_data[10] = pre_skip & 0xFF;
    header_data[11] = (pre_skip >> 8) & 0xFF;

    // 原始采样率（32位小端）
    header_data[12] = sample_rate & 0xFF;
    header_data[13] = (sample_rate >> 8) & 0xFF;
    header_data[14] = (sample_rate >> 16) & 0xFF;
    header_data[15] = (sample_rate >> 24) & 0xFF;

    // 输出增益（16位小端）- 0 dB
    header_data[16] = 0;
    header_data[17] = 0;

    // 声道映射族（1字节）- 0表示单声道/立体声
    header_data[18] = 0;

    return 19;  // OpusHead头部大小
}

/**
 * 创建OpusTags数据包
 */
static int create_opus_tags(unsigned char *tags_data) {
    const char *vendor = "libopus";
    const char *comment = "Encoded by mic_dev";

    int pos = 0;

    // OpusTags标识符（8字节）
    memcpy(tags_data + pos, "OpusTags", 8);
    pos += 8;

    // Vendor字符串长度（32位小端）
    int vendor_len = strlen(vendor);
    tags_data[pos++] = vendor_len & 0xFF;
    tags_data[pos++] = (vendor_len >> 8) & 0xFF;
    tags_data[pos++] = (vendor_len >> 16) & 0xFF;
    tags_data[pos++] = (vendor_len >> 24) & 0xFF;

    // Vendor字符串
    memcpy(tags_data + pos, vendor, vendor_len);
    pos += vendor_len;

    // 用户注释数量（32位小端）- 1个注释
    tags_data[pos++] = 1;
    tags_data[pos++] = 0;
    tags_data[pos++] = 0;
    tags_data[pos++] = 0;

    // 注释长度（32位小端）
    int comment_len = strlen(comment);
    tags_data[pos++] = comment_len & 0xFF;
    tags_data[pos++] = (comment_len >> 8) & 0xFF;
    tags_data[pos++] = (comment_len >> 16) & 0xFF;
    tags_data[pos++] = (comment_len >> 24) & 0xFF;

    // 注释字符串
    memcpy(tags_data + pos, comment, comment_len);
    pos += comment_len;

    return pos;  // 总大小
}

/**
 * 将PCM数据转换为16kHz单声道Opus格式并保存（使用libopus + Ogg容器）
 */
int convert_pcm_to_opus_file(const int16_t *pcm_data, size_t pcm_size_bytes,
                            int sample_rate, int channels, const char *opus_filename) {
    LOG_INFO(MODULE_SPEECH_UPLOAD, "🔄 Converting PCM to 16kHz mono Opus: %dHz, %dch, %.2f KB",
           sample_rate, channels, pcm_size_bytes / 1024.0);

    // 调试：打印PCM数据的前几个样本
    if (pcm_size_bytes >= 16) {
        const int16_t *samples = (const int16_t*)pcm_data;
        LOG_INFO(MODULE_SPEECH_UPLOAD, "🔍 PCM first 8 samples: [%d,%d,%d,%d,%d,%d,%d,%d]",
               samples[0], samples[1], samples[2], samples[3],
               samples[4], samples[5], samples[6], samples[7]);
    }

    // 步骤1：转换为16kHz单声道
    size_t input_samples = pcm_size_bytes / sizeof(int16_t);
    size_t output_samples = 0;
    int16_t *resampled_data = NULL;

    if (sample_rate != 16000 || channels != 1) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "🔄 Resampling %dHz %dch -> 16kHz 1ch", sample_rate, channels);
        resampled_data = convert_audio_format_speex(pcm_data, input_samples,
                                                   sample_rate, channels,
                                                   16000, 1, &output_samples);
        if (!resampled_data) {
            LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Error: Failed to resample audio");
            return -1;
        }
        LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ Resampled: %zu -> %zu samples", input_samples, output_samples);
    } else {
        // 已经是16kHz单声道，直接使用
        resampled_data = (int16_t*)pcm_data;
        output_samples = input_samples;
        LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ Audio already in 16kHz mono format");
    }

    // 步骤2：初始化Opus编码器（16kHz单声道）
    int err;
    OpusEncoder *encoder = opus_encoder_create(16000, 1, OPUS_APPLICATION_AUDIO, &err);
    if (err != OPUS_OK) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Error: Failed to create Opus encoder: %s", opus_strerror(err));
        return -1;
    }

    // 设置比特率
    opus_encoder_ctl(encoder, OPUS_SET_BITRATE(OPUS_BITRATE));
    LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ Opus encoder created: 16kHz, 1ch, %d bps", OPUS_BITRATE);

    // 打开输出文件
    FILE *fout = fopen(opus_filename, "wb");
    if (!fout) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Error: Failed to create output file: %s", opus_filename);
        opus_encoder_destroy(encoder);
        return -1;
    }

    // 初始化Ogg流
    ogg_stream_state ogg_state;
    ogg_page ogg_page;
    ogg_packet ogg_packet;

    // 使用随机序列号
    srand(time(NULL));
    int serial_no = rand();
    ogg_stream_init(&ogg_state, serial_no);

    // 1. 创建并写入OpusHead头数据包
    unsigned char header_data[19];
    int header_size = create_opus_header(header_data, sample_rate, channels);

    ogg_packet.packet = header_data;
    ogg_packet.bytes = header_size;
    ogg_packet.b_o_s = 1;  // 流开始
    ogg_packet.e_o_s = 0;  // 不是流结束
    ogg_packet.granulepos = 0;
    ogg_packet.packetno = 0;

    ogg_stream_packetin(&ogg_state, &ogg_packet);

    // 2. 创建并写入OpusTags数据包
    unsigned char tags_data[256];  // 足够大的缓冲区
    int tags_size = create_opus_tags(tags_data);

    ogg_packet.packet = tags_data;
    ogg_packet.bytes = tags_size;
    ogg_packet.b_o_s = 0;  // 不是流开始
    ogg_packet.e_o_s = 0;  // 不是流结束
    ogg_packet.granulepos = 0;
    ogg_packet.packetno = 1;

    ogg_stream_packetin(&ogg_state, &ogg_packet);

    // 写入头页面（包含OpusHead和OpusTags）
    while (ogg_stream_flush(&ogg_state, &ogg_page)) {
        fwrite(ogg_page.header, 1, ogg_page.header_len, fout);
        fwrite(ogg_page.body, 1, ogg_page.body_len, fout);
    }

    LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ OpusHead and OpusTags written to file");

    // 分配缓冲区（16kHz单声道）
    opus_int16 *frame_buffer = malloc(OPUS_FRAME_SIZE * 1 * sizeof(opus_int16));
    unsigned char *opus_packet_data = malloc(OPUS_MAX_PACKET_SIZE);
    if (!frame_buffer || !opus_packet_data) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Error: Memory allocation failed");
        fclose(fout);
        opus_encoder_destroy(encoder);
        ogg_stream_clear(&ogg_state);
        if (resampled_data != pcm_data) free(resampled_data);
        free(frame_buffer);
        free(opus_packet_data);
        return -1;
    }

    // 计算总帧数和样本数（使用重采样后的数据）
    size_t total_samples = output_samples;
    size_t samples_per_frame = OPUS_FRAME_SIZE * 1; // 单声道
    size_t total_frames = (total_samples + samples_per_frame - 1) / samples_per_frame;
    size_t encoded_frames = 0;

    // granulepos表示48kHz采样率下的累积样本数（需要根据实际采样率调整）
    // 对于16kHz数据，需要转换为48kHz等效值
    int granule_frame_size = (sample_rate == 16000) ? 320 : OPUS_FRAME_SIZE;  // 16kHz: 320, 48kHz: 960
    int pre_skip_value = (sample_rate == 16000) ? 104 : 312;
    ogg_int64_t granulepos = pre_skip_value;  // 从pre_skip开始
    ogg_int64_t packetno = 2;      // 从2开始（0=OpusHead, 1=OpusTags）

    LOG_INFO(MODULE_SPEECH_UPLOAD, "🔍 Processing %zu samples in %zu frames (16kHz mono)", total_samples, total_frames);

    // 编码循环（使用重采样后的数据）
    const int16_t *input_ptr = resampled_data;
    size_t remaining_samples = total_samples;

    while (remaining_samples > 0) {
        // 准备当前帧的数据
        size_t current_frame_samples = (remaining_samples >= samples_per_frame) ?
                                      samples_per_frame : remaining_samples;

        // 复制数据到帧缓冲区
        memcpy(frame_buffer, input_ptr, current_frame_samples * sizeof(int16_t));

        // 如果不足一帧，用0填充
        if (current_frame_samples < samples_per_frame) {
            memset(frame_buffer + current_frame_samples, 0,
                   (samples_per_frame - current_frame_samples) * sizeof(int16_t));
        }

        // 编码为Opus
        int packet_size = opus_encode(encoder, frame_buffer, OPUS_FRAME_SIZE,
                                     opus_packet_data, OPUS_MAX_PACKET_SIZE);
        if (packet_size < 0) {
            LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Error: Encoding failed: %s", opus_strerror(packet_size));
            break;
        }

        // 更新granulepos（根据实际采样率调整）
        granulepos += granule_frame_size;

        // 创建Ogg数据包
        ogg_packet.packet = opus_packet_data;
        ogg_packet.bytes = packet_size;
        ogg_packet.b_o_s = 0;
        ogg_packet.e_o_s = (remaining_samples <= samples_per_frame) ? 1 : 0;  // 最后一帧
        ogg_packet.granulepos = granulepos;
        ogg_packet.packetno = packetno++;

        // 调试：打印数据包信息
        if (encoded_frames < 3 || encoded_frames % 100 == 0) {
            LOG_INFO(MODULE_SPEECH_UPLOAD, "🔍 Packet %ld: size=%d, granulepos=%ld, e_o_s=%d",
                   (long)(packetno-1), packet_size, (long)granulepos, ogg_packet.e_o_s);
        }

        ogg_stream_packetin(&ogg_state, &ogg_packet);

        // 写入页面
        while (ogg_stream_pageout(&ogg_state, &ogg_page)) {
            fwrite(ogg_page.header, 1, ogg_page.header_len, fout);
            fwrite(ogg_page.body, 1, ogg_page.body_len, fout);
        }

        encoded_frames++;

        // 移动到下一帧
        input_ptr += current_frame_samples;
        remaining_samples -= current_frame_samples;
    }

    // 刷新剩余的页面
    while (ogg_stream_flush(&ogg_state, &ogg_page)) {
        fwrite(ogg_page.header, 1, ogg_page.header_len, fout);
        fwrite(ogg_page.body, 1, ogg_page.body_len, fout);
    }

    // 清理资源
    free(frame_buffer);
    free(opus_packet_data);
    if (resampled_data != pcm_data) free(resampled_data); // 清理重采样数据
    fclose(fout);
    opus_encoder_destroy(encoder);
    ogg_stream_clear(&ogg_state);

    if (remaining_samples == 0) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ PCM converted to Opus successfully: %s", opus_filename);
        LOG_INFO(MODULE_SPEECH_UPLOAD, "📊 Encoded %zu frames with Ogg container", encoded_frames);

        // 获取文件大小
        struct stat file_stat;
        if (stat(opus_filename, &file_stat) == 0) {
            LOG_INFO(MODULE_SPEECH_UPLOAD, "📊 Output file size: %.2f KB", file_stat.st_size / 1024.0);
        }

        return 0;
    } else {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Error: Encoding incomplete");
        unlink(opus_filename);  // 删除不完整的文件
        return -1;
    }
}

// 静态重采样器，避免重复创建
static AppResamplerWrapper g_opus_resampler = {0};
static bool g_opus_resampler_initialized = false;

/**
 * 初始化OPUS转换用的重采样器
 */
static int init_opus_resampler(void) {
    if (g_opus_resampler_initialized) {
        return 0;
    }

    // 初始化48kHz 2ch -> 16kHz 1ch重采样器
    if (app_resampler_wrapper_init(&g_opus_resampler, 48000, 16000, 2, 8) != 0) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Failed to initialize OPUS resampler");
        return -1;
    }

    g_opus_resampler_initialized = true;
    LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ OPUS resampler initialized (48kHz 2ch -> 16kHz 1ch)");
    return 0;
}

/**
 * 清理OPUS转换用的重采样器
 */
static void cleanup_opus_resampler(void) {
    if (g_opus_resampler_initialized) {
        app_resampler_wrapper_cleanup(&g_opus_resampler);
        g_opus_resampler_initialized = false;
        LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ OPUS resampler cleaned up");
    }
}

/**
 * 将PCM数据转换为OPUS数据（内存中）- 使用静态重采样器
 */
int convert_audio_to_opus_data(const int16_t *pcm_data, size_t pcm_size_bytes,
                              int sample_rate, int channels,
                              uint8_t *opus_buffer, size_t opus_buffer_size, size_t *opus_size) {
    if (!pcm_data || !opus_buffer || !opus_size) {
        return -1;
    }

    // 初始化重采样器（只初始化一次）
    if (init_opus_resampler() != 0) {
        return -1;
    }

    // 步骤1：转换为16kHz单声道
    size_t input_samples = pcm_size_bytes / sizeof(int16_t);

    if (sample_rate == 48000 && channels == 2) {
        // 使用静态重采样器
        int input_frames = input_samples / 2; // 2声道
        int16_t resampled_buffer[8192]; // 足够大的缓冲区
        int output_frames = 0;

        // 重采样：48kHz 2ch -> 16kHz 2ch
        if (app_resampler_wrapper_process(&g_opus_resampler, pcm_data, input_frames,
                                         resampled_buffer, sizeof(resampled_buffer)/sizeof(int16_t)/2,
                                         &output_frames) != 0) {
            LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Failed to resample audio for OPUS conversion");
            return -1;
        }

        if (output_frames <= 0) {
            LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ No output frames from OPUS resampler");
            return -1;
        }

        // 转换为单声道：取左声道
        int16_t mono_buffer[4096];
        int mono_samples = 0;
        for (int i = 0; i < output_frames && mono_samples < 4096; i++) {
            mono_buffer[mono_samples++] = resampled_buffer[i * 2]; // 取左声道
        }

        // 步骤2：模拟OPUS压缩（实际应该调用libopus）
        size_t compressed_size = mono_samples * sizeof(int16_t) / 4; // 模拟4:1压缩比
        if (compressed_size > opus_buffer_size) {
            LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ OPUS buffer too small: need %zu, have %zu", compressed_size, opus_buffer_size);
            return -1;
        }

        // 简化：只复制前面的数据作为"压缩"结果
        memcpy(opus_buffer, mono_buffer, compressed_size);
        *opus_size = compressed_size;

        // 减少日志输出
        static int log_counter = 0;
        if (++log_counter % 50 == 0) {
            LOG_INFO(MODULE_SPEECH_UPLOAD, "🔄 OPUS conversion: %zu bytes PCM -> %zu bytes 'OPUS' (16kHz mono, count: %d)",
                   pcm_size_bytes, *opus_size, log_counter);
        }

        return 0;
    } else {
        // 不支持的格式
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Unsupported audio format for OPUS conversion: %dHz %dch", sample_rate, channels);
        return -1;
    }
}

/**
 * 使用SpeexDSP进行高质量重采样和声道转换
 */
static int16_t* convert_audio_format_speex(const int16_t *input_data, size_t input_samples,
                                          int input_rate, int input_channels,
                                          int output_rate, int output_channels,
                                          size_t *output_samples) {
    if (!input_data || input_samples == 0) return NULL;

    LOG_INFO(MODULE_SPEECH_UPLOAD, "🔄 SpeexDSP Converting: %dHz %dch → %dHz %dch (%zu samples)",
           input_rate, input_channels, output_rate, output_channels, input_samples);

    // 计算输出缓冲区大小（预估，稍微大一些）
    size_t estimated_output = (input_samples * output_rate / input_rate) + 1024;
    int16_t *output_data = malloc(estimated_output * sizeof(int16_t));
    if (!output_data) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Error: Failed to allocate output buffer");
        return NULL;
    }

    // 使用应用重采样器
    int actual_output_samples;
    if (app_resample_audio(input_data, (int)input_samples, input_rate, input_channels,
                          output_data, (int)estimated_output, output_rate, output_channels,
                          &actual_output_samples) != 0) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Error: SpeexDSP resampling failed");
        free(output_data);
        return NULL;
    }

    *output_samples = (size_t)actual_output_samples;

    LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ SpeexDSP conversion successful: %zu → %zu samples",
           input_samples, *output_samples);

    return output_data;
}

/**
 * 简单的JSON文本提取函数
 * 从Whisper返回的JSON中提取segments里的text内容
 */
char* extract_text_from_whisper_json(const char *json_str) {
    if (!json_str) return NULL;

    // 查找segments数组
    const char *segments_start = strstr(json_str, "\"segments\":");
    if (!segments_start) {
        // 如果没有segments，可能是简单的text字段
        const char *text_start = strstr(json_str, "\"text\":");
        if (text_start) {
            text_start += 7; // 跳过"text":
            while (*text_start && isspace(*text_start)) text_start++; // 跳过空格
            if (*text_start == '"') text_start++; // 跳过开始引号

            const char *text_end = strchr(text_start, '"');
            if (text_end) {
                size_t len = text_end - text_start;
                char *result = malloc(len + 1);
                if (result) {
                    strncpy(result, text_start, len);
                    result[len] = '\0';
                    return result;
                }
            }
        }
        return NULL;
    }

    // 查找第一个segment的text字段
    const char *first_segment = strchr(segments_start, '{');
    if (!first_segment) return NULL;

    const char *text_field = strstr(first_segment, "\"text\":");
    if (!text_field) return NULL;

    text_field += 7; // 跳过"text":
    while (*text_field && isspace(*text_field)) text_field++; // 跳过空格
    if (*text_field != '"') return NULL;
    text_field++; // 跳过开始引号

    const char *text_end = strchr(text_field, '"');
    if (!text_end) return NULL;

    size_t len = text_end - text_field;
    char *result = malloc(len + 1);
    if (result) {
        strncpy(result, text_field, len);
        result[len] = '\0';

        // 去除前后空格
        char *start = result;
        while (*start && isspace(*start)) start++;

        char *end = result + strlen(result) - 1;
        while (end > start && isspace(*end)) *end-- = '\0';

        // 如果去除空格后为空，返回NULL
        if (strlen(start) == 0) {
            free(result);
            return NULL;
        }

        // 移动内容到开头
        if (start != result) {
            memmove(result, start, strlen(start) + 1);
        }
    }

    return result;
}

/**
 * 创建识别结果结构体
 */
static SpeechRecognitionResult* create_result(void) {
    SpeechRecognitionResult *result = malloc(sizeof(SpeechRecognitionResult));
    if (!result) return NULL;

    memset(result, 0, sizeof(SpeechRecognitionResult));
    return result;
}

/**
 * 上传PCM文件进行语音识别
 */
SpeechRecognitionResult* speech_upload_pcm_file(const char *pcm_file_path, const char *server_url) {
    if (!pcm_file_path) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Error: PCM file path is NULL");
        return NULL;
    }
    
    // 检查文件是否存在
    struct stat file_stat;
    if (stat(pcm_file_path, &file_stat) != 0) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Error: PCM file does not exist: %s", pcm_file_path);
        return NULL;
    }
    
    SpeechRecognitionResult *result = create_result();
    if (!result) return NULL;
    
    clock_t start_time = clock();
    
    // 使用指定的URL或默认URL
    const char *url = server_url ? server_url : g_server_url;
    
    CURL *curl = curl_easy_init();
    if (!curl) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Error: Failed to initialize CURL");
        result->success = false;
        result->error_message = strdup("Failed to initialize CURL");
        return result;
    }
    
    // 准备响应缓冲区
    UploadResponse response = {0};
    response.capacity = MAX_RESPONSE_SIZE;
    response.data = malloc(response.capacity);
    if (!response.data) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Error: Failed to allocate response buffer");
        curl_easy_cleanup(curl);
        result->success = false;
        result->error_message = strdup("Failed to allocate response buffer");
        return result;
    }
    
    // 设置CURL选项
    curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "POST");
    curl_easy_setopt(curl, CURLOPT_URL, url);
    curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);  // 30秒超时
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_response_callback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
    
    // 设置HTTP头
    struct curl_slist *headers = NULL;
    headers = curl_slist_append(headers, "Content-Type: multipart/form-data");
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    
    // 设置multipart表单数据
    curl_mime *mime = curl_mime_init(curl);
    curl_mimepart *part = curl_mime_addpart(mime);
    curl_mime_name(part, "file");
    curl_mime_filedata(part, pcm_file_path);
    curl_easy_setopt(curl, CURLOPT_MIMEPOST, mime);
    
    LOG_INFO(MODULE_SPEECH_UPLOAD, "🌐 Uploading audio file to ASR server...");
    LOG_INFO(MODULE_SPEECH_UPLOAD, "📁 File: %s (%.2f KB)", pcm_file_path, file_stat.st_size / 1024.0);
    LOG_INFO(MODULE_SPEECH_UPLOAD, "🔗 Server: %s", url);
    
    // 执行请求
    CURLcode res = curl_easy_perform(curl);
    
    // 获取HTTP状态码
    long http_code = 0;
    curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);
    result->http_code = (int)http_code;
    
    // 计算上传时间
    clock_t end_time = clock();
    result->upload_time = ((double)(end_time - start_time)) / CLOCKS_PER_SEC;
    
    if (res != CURLE_OK) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Upload failed: %s", curl_easy_strerror(res));
        result->success = false;
        result->error_message = strdup(curl_easy_strerror(res));
    } else if (http_code == 200) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ Upload successful (%.2fs)", result->upload_time);
        LOG_INFO(MODULE_SPEECH_UPLOAD, "📝 Response: %s", response.data);
        
        result->success = true;
        result->transcription = strdup(response.data);
    } else {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Server error: HTTP %ld", http_code);
        LOG_INFO(MODULE_SPEECH_UPLOAD, "📝 Response: %s", response.data);
        
        result->success = false;
        char error_msg[256];
        snprintf(error_msg, sizeof(error_msg), "HTTP %ld: %s", http_code, response.data);
        result->error_message = strdup(error_msg);
    }
    
    // 清理资源
    curl_mime_free(mime);
    curl_slist_free_all(headers);
    curl_easy_cleanup(curl);
    free(response.data);
    
    return result;
}

/**
 * 上传音频数据进行语音识别（自动转换为Whisper兼容格式）
 */
SpeechRecognitionResult* speech_upload_audio_data(const int16_t *audio_data, size_t data_size,
                                                 int sample_rate, int channels,
                                                 const char *server_url __attribute__((unused))) {
    if (!audio_data || data_size == 0) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Error: Invalid audio data");
        return NULL;
    }

    LOG_INFO(MODULE_SPEECH_UPLOAD, "🎵 Original audio: %dHz, %dch, %.2fs, %zu bytes",
           sample_rate, channels, (double)data_size / (sample_rate * channels * sizeof(int16_t)), data_size);

    // 验证输入数据的有效性
    if (data_size == 0) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Error: No audio data to process");
        return NULL;
    }

    // 调试：打印原始音频的前几个样本
    if (data_size >= 16) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "🔍 Original audio first 8 samples: [%d,%d,%d,%d,%d,%d,%d,%d]",
               audio_data[0], audio_data[1], audio_data[2], audio_data[3],
               audio_data[4], audio_data[5], audio_data[6], audio_data[7]);
    }

    // 使用Opus格式，直接处理原始PCM数据，无需重采样
    LOG_INFO(MODULE_SPEECH_UPLOAD, "🎵 Using Opus format - no resampling needed!");
    LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ Direct PCM to Opus conversion: %dHz, %dch", sample_rate, channels);

    // 创建保存的Opus文件
    char saved_opus_filename[256];
    snprintf(saved_opus_filename, sizeof(saved_opus_filename), "/tmp/api_upload_%ld.opus", time(NULL));

    // 直接转换PCM为Opus格式
    if (convert_pcm_to_opus_file(audio_data, data_size, sample_rate, channels, saved_opus_filename) != 0) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Error: Failed to convert to Opus format");
        return NULL;
    }

    LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ Audio converted to Opus format");
    LOG_INFO(MODULE_SPEECH_UPLOAD, "📁 Opus file saved: %s", saved_opus_filename);
    LOG_INFO(MODULE_SPEECH_UPLOAD, "🎵 You can play it with: opusdec %s - | aplay -f S16_LE -r 48000 -c 2", saved_opus_filename);

    // 上传Opus文件到语音识别服务器
    LOG_INFO(MODULE_SPEECH_UPLOAD, "🌐 Uploading Opus file to speech recognition server...");
    SpeechRecognitionResult *result = speech_upload_pcm_file(saved_opus_filename, server_url);

    return result;
}

/**
 * 释放识别结果
 */
void speech_result_free(SpeechRecognitionResult *result) {
    if (!result) return;
    
    if (result->transcription) {
        free(result->transcription);
    }
    if (result->error_message) {
        free(result->error_message);
    }
    free(result);
}

/**
 * 打印识别结果
 */
void speech_result_print(const SpeechRecognitionResult *result) {
    if (!result) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ No result to print");
        return;
    }
    
    LOG_INFO(MODULE_SPEECH_UPLOAD, "\n=== Speech Recognition Result ===");
    LOG_INFO(MODULE_SPEECH_UPLOAD, "Success: %s", result->success ? "✅ Yes" : "❌ No");
    LOG_INFO(MODULE_SPEECH_UPLOAD, "HTTP Code: %d", result->http_code);
    LOG_INFO(MODULE_SPEECH_UPLOAD, "Upload Time: %.2f seconds", result->upload_time);
    
    if (result->success && result->transcription) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Transcription: 📝 %s", result->transcription);
    }
    
    if (!result->success && result->error_message) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Error: ❌ %s", result->error_message);
    }
    LOG_INFO(MODULE_SPEECH_UPLOAD, "================================\n");
}

/**
 * 设置默认服务器URL
 */
void speech_upload_set_server_url(const char *url) {
    if (url && strlen(url) < MAX_URL_SIZE) {
        strncpy(g_server_url, url, MAX_URL_SIZE - 1);
        g_server_url[MAX_URL_SIZE - 1] = '\0';
        LOG_INFO(MODULE_SPEECH_UPLOAD, "ASR server URL updated: %s", g_server_url);
    }
}

/**
 * 获取当前服务器URL
 */
const char* speech_upload_get_server_url(void) {
    return g_server_url;
}

// ==================== TTS功能实现 ====================

/**
 * 创建TTS结果结构体
 */
static TTSResult* create_tts_result(void) {
    TTSResult *result = malloc(sizeof(TTSResult));
    if (!result) return NULL;

    memset(result, 0, sizeof(TTSResult));
    return result;
}

/**
 * 发送文本到TTS服务器进行播报
 */
TTSResult* tts_speak_text(const char *text, bool immediate, const char *server_url) {
    if (!text || strlen(text) == 0) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Error: Text is empty");
        return NULL;
    }

    TTSResult *result = create_tts_result();
    if (!result) return NULL;

    clock_t start_time = clock();

    // 使用指定的URL或默认URL
    const char *url = server_url ? server_url : g_tts_server_url;

    CURL *curl = curl_easy_init();
    if (!curl) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Error: Failed to initialize CURL for TTS");
        result->success = false;
        result->error_message = strdup("Failed to initialize CURL");
        return result;
    }

    // 准备响应缓冲区
    UploadResponse response = {0};
    response.capacity = MAX_RESPONSE_SIZE;
    response.data = malloc(response.capacity);
    if (!response.data) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Error: Failed to allocate TTS response buffer");
        curl_easy_cleanup(curl);
        result->success = false;
        result->error_message = strdup("Failed to allocate response buffer");
        return result;
    }

    // 构建JSON数据
    char json_data[MAX_TEXT_SIZE + 100];
    snprintf(json_data, sizeof(json_data),
             "{\r\n    \"text\": \"%s\",\r\n    \"immediate\": %s\r\n}",
             text, immediate ? "true" : "false");

    // 设置CURL选项
    curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "POST");
    curl_easy_setopt(curl, CURLOPT_URL, url);
    curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);  // 10秒超时
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_response_callback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, json_data);

    // 设置HTTP头
    struct curl_slist *headers = NULL;
    headers = curl_slist_append(headers, "Content-Type: application/json");
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

    LOG_INFO(MODULE_SPEECH_UPLOAD, "🔊 Sending text to TTS server...");
    LOG_INFO(MODULE_SPEECH_UPLOAD, "📝 Text: %s", text);
    LOG_INFO(MODULE_SPEECH_UPLOAD, "⚡ Mode: %s", immediate ? "Immediate" : "Queued");
    LOG_INFO(MODULE_SPEECH_UPLOAD, "🔗 Server: %s", url);

    // 执行请求
    CURLcode res = curl_easy_perform(curl);

    // 获取HTTP状态码
    long http_code = 0;
    curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);
    result->http_code = (int)http_code;

    // 计算请求时间
    clock_t end_time = clock();
    result->request_time = ((double)(end_time - start_time)) / CLOCKS_PER_SEC;

    if (res != CURLE_OK) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ TTS request failed: %s", curl_easy_strerror(res));
        result->success = false;
        result->error_message = strdup(curl_easy_strerror(res));
    } else if (http_code == 200) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ TTS request successful (%.2fs)", result->request_time);
        LOG_INFO(MODULE_SPEECH_UPLOAD, "📝 Server response: %s", response.data);

        result->success = true;
        result->response_text = strdup(response.data);
    } else {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ TTS server error: HTTP %ld", http_code);
        LOG_INFO(MODULE_SPEECH_UPLOAD, "📝 Response: %s", response.data);

        result->success = false;
        char error_msg[256];
        snprintf(error_msg, sizeof(error_msg), "HTTP %ld: %s", http_code, response.data);
        result->error_message = strdup(error_msg);
    }

    // 清理资源
    curl_slist_free_all(headers);
    curl_easy_cleanup(curl);
    free(response.data);

    return result;
}





// 全局变量用于等待识别结果
static SpeechRecognitionResult *g_pending_result = NULL;
static bool g_result_received = false;
static pthread_mutex_t g_result_mutex = PTHREAD_MUTEX_INITIALIZER;
static pthread_cond_t g_result_cond = PTHREAD_COND_INITIALIZER;

/**
 * 阿里云ASR结果处理函数（由main.c中的回调调用）
 */
void ali_asr_process_result(const char *text, bool is_final) {
    pthread_mutex_lock(&g_result_mutex);

    if (g_pending_result && is_final && text && strlen(text) > 0) {
        if (g_pending_result->transcription) {
            free(g_pending_result->transcription);
        }
        g_pending_result->transcription = strdup(text);
        g_pending_result->success = true;
        g_result_received = true;
        pthread_cond_signal(&g_result_cond);

        LOG_INFO(MODULE_SPEECH_UPLOAD, "📝 Ali ASR final result received: %s", text);
    }

    pthread_mutex_unlock(&g_result_mutex);
}

/**
 * 使用阿里云实时语音识别处理音频数据
 * 改为流式发送并等待结果
 */
SpeechRecognitionResult* speech_upload_audio_data_ali(const int16_t *audio_data, size_t data_size,
                                                     int sample_rate, int channels) {
    if (!audio_data || data_size == 0) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Invalid audio data for Ali ASR");
        return NULL;
    }

    LOG_INFO(MODULE_SPEECH_UPLOAD, "🎵 Processing audio with Ali ASR: %dHz, %dch, %.2fs, %zu bytes",
           sample_rate, channels, (double)data_size / (sample_rate * channels * sizeof(int16_t)), data_size);

    clock_t start_time = clock();

    // 检查ASR是否已准备好
    if (!ali_asr_is_ready()) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "⚠️ Ali ASR not ready");
        goto error_exit;
    }

    // 准备结果结构
    pthread_mutex_lock(&g_result_mutex);
    if (g_pending_result) {
        speech_result_free(g_pending_result);
    }
    g_pending_result = malloc(sizeof(SpeechRecognitionResult));
    if (g_pending_result) {
        memset(g_pending_result, 0, sizeof(SpeechRecognitionResult));
        g_pending_result->success = false;
        g_pending_result->http_code = 200;
    }
    g_result_received = false;
    pthread_mutex_unlock(&g_result_mutex);

    if (!g_pending_result) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Failed to allocate result structure");
        goto error_exit;
    }

    // 流式发送音频数据
    LOG_INFO(MODULE_SPEECH_UPLOAD, "🚀 Starting streaming audio to Ali ASR...");
    if (ali_asr_send_audio(audio_data, data_size) != 0) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Failed to send audio data to Ali ASR");
        goto error_exit;
    }

    // 等待识别结果（最多等待10秒）
    LOG_INFO(MODULE_SPEECH_UPLOAD, "⏳ Waiting for Ali ASR recognition result...");
    struct timespec timeout;
    struct timeval now;
    gettimeofday(&now, NULL);
    timeout.tv_sec = now.tv_sec + 10;
    timeout.tv_nsec = now.tv_usec * 1000;

    pthread_mutex_lock(&g_result_mutex);
    int wait_result = 0;
    while (!g_result_received && wait_result == 0) {
        wait_result = pthread_cond_timedwait(&g_result_cond, &g_result_mutex, &timeout);
    }

    SpeechRecognitionResult *result = g_pending_result;
    g_pending_result = NULL;
    pthread_mutex_unlock(&g_result_mutex);

    // 计算处理时间
    clock_t end_time = clock();
    double processing_time = ((double)(end_time - start_time)) / CLOCKS_PER_SEC;

    if (result) {
        result->upload_time = processing_time;

        if (wait_result == 0 && g_result_received) {
            LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ Ali ASR completed successfully in %.2fs", processing_time);
        } else {
            LOG_INFO(MODULE_SPEECH_UPLOAD, "⚠️ Ali ASR timeout after %.2fs", processing_time);
            result->success = false;
            if (result->transcription) {
                free(result->transcription);
            }
            result->transcription = strdup("Ali ASR timeout");
        }
    }

    return result;

error_exit:
    // 创建失败结果
    clock_t error_end_time = clock();
    double error_processing_time = ((double)(error_end_time - start_time)) / CLOCKS_PER_SEC;

    SpeechRecognitionResult *error_result = malloc(sizeof(SpeechRecognitionResult));
    if (error_result) {
        memset(error_result, 0, sizeof(SpeechRecognitionResult));
        error_result->success = false;
        error_result->upload_time = error_processing_time;
        error_result->error_message = strdup("Ali ASR not ready");
    }

    return error_result;
}

/**
 * 释放TTS结果
 */
void tts_result_free(TTSResult *result) {
    if (!result) return;

    if (result->response_text) {
        free(result->response_text);
    }
    if (result->error_message) {
        free(result->error_message);
    }
    free(result);
}

/**
 * 打印TTS结果
 */
void tts_result_print(const TTSResult *result) {
    if (!result) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ No TTS result to print");
        return;
    }

    LOG_INFO(MODULE_SPEECH_UPLOAD, "\n=== TTS播报结果 ===");
    LOG_INFO(MODULE_SPEECH_UPLOAD, "Success: %s", result->success ? "✅ Yes" : "❌ No");
    LOG_INFO(MODULE_SPEECH_UPLOAD, "HTTP Code: %d", result->http_code);
    LOG_INFO(MODULE_SPEECH_UPLOAD, "Request Time: %.2f seconds", result->request_time);

    if (result->success && result->response_text) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Response: 📝 %s", result->response_text);
    }

    if (!result->success && result->error_message) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Error: ❌ %s", result->error_message);
    }
    LOG_INFO(MODULE_SPEECH_UPLOAD, "==================\n");
}

/**
 * 设置默认TTS服务器URL
 */
void tts_set_server_url(const char *url) {
    if (url && strlen(url) < MAX_URL_SIZE) {
        strncpy(g_tts_server_url, url, MAX_URL_SIZE - 1);
        g_tts_server_url[MAX_URL_SIZE - 1] = '\0';
        LOG_INFO(MODULE_SPEECH_UPLOAD, "TTS server URL updated: %s", g_tts_server_url);
    }
}

/**
 * 获取当前TTS服务器URL
 */
const char* tts_get_server_url(void) {
    return g_tts_server_url;
}

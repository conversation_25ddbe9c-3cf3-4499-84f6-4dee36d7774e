UniASR 模型是一种2遍刷新模型（Two pass）端到端语音识别模型。
日益丰富的业务需求，不仅要求识别效果精度高，而且要求能够实时地进行语音识别。一方面，离线语音识别系统具有较高的识别准确率，但其无法实时的返回解码文字结果，并且，在处理长语音时，容易发生解码重复的问题，以及高并发解码超时的问题等；另一方面，流式系统能够低延时的实时进行语音识别，但由于缺少下文信息，流式语音识别系统的准确率不如离线系统，在流式业务场景中，为了更好的折中实时性与准确率，往往采用多个不同时延的模型系统。
为了满足差异化业务场景对计算复杂度、实时性和准确率的要求，常用的做法是维护多种语音识别系统，例如，CTC系统、E2E离线系统、SCAMA流式系统等。在不同的业务场景使用不同的模型和系统，不仅会增加模型生产成本和迭代周期，而且会增加引擎以及服务部署的维护成本。因此，我们设计了离线流式一体化语音识别系统——UniASR。UniASR同时具有高精度和低延时的特点，不仅能够实时输出语音识别结果，而且能够在说话句尾用高精度的解码结果修正输出，与此同时，UniASR采用动态延时训练的方式，替代了之前维护多套延时流式系统的做法。
通过设计UniASR语音识别系统，我们将之前多套语音识别系统架构统一为一套系统架构，一个模型满足所有业务场景，显著的降低了模型生产和维护成本。
UniASR模型结构如上图所示，包含离线语音识别部分和流式语音识别部分。其中，离线与流式部分通过共享一个动态编码器（Encoder）结构来降低计算量。
流式语音识别部分是由动态时延 Encoder 与流式解码器（Decoder）构成。动态时延 Encoder 采用时延受限有句记忆单元的自注意力（LC-SAN-M）结构；流式 Decoder 采用动态 SCAMA 结构。
离线语音识别部分包含了降采样层（Sride Conv）、Big-Chunk Encoder、文本Encoder与SCAMA Decoder。
为了降低刷新输出结果的尾点延时，离线识别部分采用大Chunk 流式结构。其中，Stride Conv结构是为了降低计算量。文本 Encoder 增加了离线识别的语义信息。为了让模型能够具有不同延时下进行语音识别的能力，我们创新性地设计了动态时延训练机制，使得模型能够同时满足不同业务场景对延时和准确率的要求。根据业务场景特征，我们将语音识别需求大致分为3类：
低延迟实时听写：如电话客服，IOT语音交互等，该场景对于尾点延迟非常敏感，通常需要用户说完以后立马可以得到识别结果。
流式实时听写：如会议实时字幕，语音输入法等，该场景不仅要求能够实时返回语音识别结果，以便实时显示到屏幕上，而且还需要能够在说话句尾用高精度识别结果刷新输出。
离线文件转写：如音频转写，视频字幕生成等，该场景不对实时性有要求，要求在高识别准确率情况下，尽可能快的转录文字。
为了同时满足上面3种业务场景需求，我们将模型分成3种解码模式，分别对应为：
ast 模式：只有一遍解码，采用低延时实时出字模式；  
normal 模式：2遍解码，第一遍低延时实时出字上屏，第二遍间隔3～6s（可配置）对解码结果进行刷新；  
offline 模式：只有一遍解码，采用高精度离线模式；  
在模型部署阶段，通过发包指定该次语音识别服务的场景模式和延时配置。这样，通过UniASR系统，我们统一了离线流式语音识别系统架构，提高模型识别效果的同时，不仅降低了模型生产成本和迭代周期，还降低了引擎以及服务部署维护成本。目前我们提供的语音识别服务基本都是基于UniASR。
现阶段只能在Linux-x86_64运行，不支持Mac和Windows。
直接推理：可以直接对输入音频进行解码，输出目标文字。
微调：加载训练好的模型，采用私有或者开源数据进行模型训练。
使用范围与目标场景
输入语音时长在60s以下。
该任务采用一种全新的域校准图像翻译模型DCT-Net（Domain-Calibrated Translation），利用小样本的风格数据，即可得到高保真、强鲁棒、易拓展的人像风格转换模型，并通过端到端推理快速得到风格转换结果。
包含人脸的人像照片，人脸分辨率大于100x100，总体图像分辨率小于3000×3000，低质人脸图像建议预先人脸增强处理。
你可以使用StructBERT中文文本相似度模型，对通用领域的文本相似度任务进行推理。 输入形如（文本A，文本B）的文本对数据，模型会给出该文本对的是否相似的标签（0, 1）以及相应的概率。
StructBERT中文文本相似度模型是在structbert-base-chinese预训练模型的基础上，用atec、bq_corpus、chineseSTS、lcqmc、paws-x-zh五个数据集（52.5w条数据，正负比例0.48:0.52）训练出来的相似度匹配模型。
本模型主要用于给输入图片输出图中文字外接框坐标，具体地，模型输出的框的坐标为文字框四边形的四个角点的坐标，左上角为第一个点，按照顺时针的顺序依次输出各个点的坐标，分别为(x1,y1)(x2,y2)(x3,y3)(x4,y4)。用户可以自行尝试各种输入图片。具体调用方式请参考代码示例。
中文分词任务就是把连续的汉字分隔成具有语言语义学意义的词汇。中文的书写习惯不像英文等日耳曼语系语言词与词之前显式的用空格分隔。为了让计算机理解中文文本，通常来说中文信息处理的第一步就是进行文本分词。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。加载训练好的模型，采用私有或者开源数据进行模型训练。
中文分词样例:
输入: 阿里巴巴集团的使命是让天下没有难做的生意
输出: 阿里巴巴/ 集团/ 的/ 使命/ 是/ 让/ 天下/ 没有/ 难/ 做/ 的/ 生意
目前提供通用新闻领域的分词模型, 采用无监督统计特征增强的StructBERT+softmax序列标注模型,序列标注标签体系(B、I、E、S),四个标签分别表示单字处理单词的起始、中间、终止位置或者该单字独立成词; 这里采用的StructBERT预训练语言模型底座可以参考 StructBERT: Incorporating Language Structures into Pre-training for Deep Language Understanding。
本模型基于PKU数据集(通用新闻领域)上训练，在垂类领域中文文本上的分词效果会有降低，请用户自行评测后决定如何使用。
模型采用新闻领域%%,%1,%d,%s,%.f %.04f本%n模型采用新闻领域分词标注数据集PKU标注训练。
数据预处理成(B、I、E、S)标签体系的数据格式, 每一个独立的单字对应一个独立的标签, 预处理后数据样例如下:
B-CWS I-CWS I-CWS E-CWS S-CWS B-CWS E-CWS B-CWS E-CWS S-CWS B-CWS E-CWS S-CWS B-CWS E-CWS B-CWS E-CWS B-CWS E-CWS S-CWS B-CWS E-CWS S-CWS%D
发现了NLS经常崩的原因了……有客户asr结果是 "，最高啊0.1%C的费用"
%C传给log4cpp就会当成cpp的%C，然后gg……以后稳定性测试 得让测试同学灌大量各种音频，而不是几条固定音频了……

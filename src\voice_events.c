#include "voice_events.h"
#include "voice_dialog.h"
#include "logger.h"
#include "websocket_server.h"
#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <stdbool.h>
#include <stdint.h>
#include "audio_covert.h"
#include "ali_asr.h"

#ifndef _WIN32

#include <sys/stat.h>

#endif

// Ali ASR连接状态和开关
static bool g_ali_asr_connected = false;

/**
 * 初始化语音事件处理器
 */
int voice_events_init(bool enable_ali_asr) {
    // 测试日志系统
    LOG_INFO(MODULE_VOICE_EVENTS, "🔧 Voice events handler initialized (Ali ASR: %s)",
             enable_ali_asr ? "enabled" : "disabled");

    return 0;
}


/**
 * 清理语音事件处理器
 */
void voice_events_cleanup(void) {

    LOG_INFO(MODULE_VOICE_EVENTS, "🔧 Voice events handler cleaned up");
}

/**
 * 1. 系统就绪事件
 */
void on_voice_ready(void) {
    LOG_INFO(MODULE_VOICE_EVENTS, "🟢 Voice dialog system ready");
}

/**
 * 2. VAD检测到人声，开始录音
 */
void on_voice_start(void) {
    LOG_INFO(MODULE_VOICE_EVENTS, "🎤 VAD detected speech! Starting recording...");

#if ALI_ASR_ENABLED
    LOG_INFO(MODULE_VOICE_EVENTS, "🔗 Connecting to Ali ASR...");
    if (ali_asr_start_connection() == 0) {
        g_ali_asr_connected = true;
        LOG_INFO(MODULE_VOICE_EVENTS, "✅ Ali ASR connected successfully");
    } else {
        g_ali_asr_connected = false;
        LOG_INFO(MODULE_VOICE_EVENTS, "❌ Failed to connect Ali ASR, continuing without it");
    }
#endif

}

/**
 * 3. 每100ms产生 buffer 数据
 */
void on_voice_process_audio(const uint8_t *buf_data, size_t buf_size, int sample_rate, int channels) {
    //LOG_INFO(MODULE_VOICE_EVENTS, "📤 Processing audio chunk: %zu bytes, %dHz, %dch", buf_size, sample_rate, channels);

    // 发送音频数据到WebSocket客户端（用于实时播放）
    websocket_send_audio_data(buf_data, buf_size, sample_rate, channels);

#if ALI_ASR_ENABLED
    if (g_ali_asr_connected) {
        //unsigned char *opus_data = NULL;
        //size_t opus_size = 0;
        //if (convert_pcm_to_opus_bytes(buf_data, buf_size, sample_rate, channels, &opus_data, &opus_size) != 0) {
        //    LOG_ERROR(MODULE_VOICE_EVENTS, "❌ Error: Failed to convert PCM to Opus bytes");
        //    return;
        //}

        // 数据已经是16k/1ch PCM，直接发送
        int ret = ali_asr_send_audio((const int16_t *)buf_data, buf_size);
        if (ret != 0) {
            LOG_ERROR(MODULE_VOICE_EVENTS, "⚠️ Ali ASR send failed");
        }
    }
#endif

}

/**
 * 4. 完整的 buffer 数据
 */
void on_voice_finish(const uint8_t *buf_data, size_t buf_size, int sample_rate, int channels) {
    LOG_INFO(MODULE_VOICE_EVENTS, "🎯 ===== RECORDING FINISHED =====");
    LOG_INFO(MODULE_VOICE_EVENTS, "📊 Total audio data: %zu bytes (%.2f seconds @ 48kHz stereo)",
             buf_data, (double) buf_size / (48000 * 2 * 2));

    // 生成OPUS文件名
    char opus_filename[256];
    snprintf(opus_filename, sizeof(opus_filename), "/tmp/voice_recording_%ld.opus", time(NULL));

    // 使用真正的OPUS编码保存（数据实际上是PCM，需要转换）
    if (buf_data && buf_size > 0) {
        // 调试：检查音频数据
        const int16_t *samples = (const int16_t *) buf_data;
        size_t sample_count = buf_size / sizeof(int16_t);
        LOG_INFO(MODULE_VOICE_EVENTS, "🔍 Audio data check: %zu samples, first 8: [%d,%d,%d,%d,%d,%d,%d,%d]",
                 sample_count, samples[0], samples[1], samples[2], samples[3],
                 samples[4], samples[5], samples[6], samples[7]);

        // 调用已有的PCM到OPUS转换函数
        // 实际数据是48kHz立体声PCM（根据VOICE_SAMPLE_RATE和VOICE_CHANNELS）
        if (convert_pcm_to_opus_file((const int16_t *) buf_data, buf_size,
                                     sample_rate, channels, opus_filename) == 0) {
            LOG_INFO(MODULE_VOICE_EVENTS, "✅ Audio saved as OPUS: %s", opus_filename);
            LOG_INFO(MODULE_VOICE_EVENTS, "🎵 Play OPUS: sudo mpv %s", opus_filename);

            // 获取文件大小
            struct stat file_stat;
            if (stat(opus_filename, &file_stat) == 0) {
                LOG_INFO(MODULE_VOICE_EVENTS, "📊 OPUS file size: %.2f KB", file_stat.st_size / 1024.0);
            }
        } else {
            LOG_INFO(MODULE_VOICE_EVENTS, "❌ Failed to convert PCM to OPUS");
        }
    } else {
        LOG_INFO(MODULE_VOICE_EVENTS, "⚠️ No audio data to save");
    }

}

/**
 * 5. 连续5秒无声音，停止录音
 */
void on_voice_stop(void) {
    LOG_INFO(MODULE_VOICE_EVENTS, "⏹️ Recording stopped (5 seconds of silence)");
#if ALI_ASR_ENABLED
    LOG_INFO(MODULE_VOICE_EVENTS, "🔌 Stopping Ali ASR connection...");
    ali_asr_stop_connection();
    g_ali_asr_connected = false;
    LOG_INFO(MODULE_VOICE_EVENTS, "✅ Ali ASR stopped");
#endif
}

/**
 * 6. 关闭连接
 */
void on_voice_close(void) {
    LOG_INFO(MODULE_VOICE_EVENTS, "🔴 Voice dialog connection closed");
#if ALI_ASR_ENABLED
    //if (g_ali_asr_connected) {
    //    LOG_INFO(MODULE_VOICE_EVENTS, "🔌 Closing Ali ASR connection...");
    //    ali_asr_stop_connection();
    //    g_ali_asr_connected = false;
    //}
#endif
}

/**
 * 设置语音事件回调到voice_dialog系统
 */
int voice_events_setup_callbacks(void *voice_dialog) {
    VoiceDialogProcessor *processor = (VoiceDialogProcessor *) voice_dialog;

    if (!processor) {
        LOG_INFO(MODULE_VOICE_EVENTS, "❌ Invalid voice dialog processor");
        return -1;
    }

    // 设置新的回调函数系统
    if (voice_dialog_set_new_callbacks(processor,
                                       on_voice_ready,        // 1. 系统就绪
                                       on_voice_start,        // 2. VAD检测到人声
                                       on_voice_process_audio, // 3. 每100ms产生OPUS数据
                                       on_voice_finish,       // 4. 完整的OPUS数据
                                       on_voice_stop,         // 5. 连续5秒无声音
                                       on_voice_close) != 0) { // 6. 关闭连接
        LOG_INFO(MODULE_VOICE_EVENTS, "❌ Failed to set voice dialog callbacks");
        return -1;
    }

    LOG_INFO(MODULE_VOICE_EVENTS, "✅ Voice events callbacks configured successfully");
    return 0;
}

#ifndef SPEECH_UPLOAD_H
#define SPEECH_UPLOAD_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include "ali_asr.h"

// 语音识别服务器配置
#define DEFAULT_ASR_SERVER_URL "http://192.168.8.42:8501/transcribe"
// TTS服务器配置
#define DEFAULT_TTS_SERVER_URL "http://************:8230/speak"
#define MAX_RESPONSE_SIZE 4096
#define MAX_URL_SIZE 512
#define MAX_TEXT_SIZE 1024

// 上传响应结构
typedef struct {
    char *data;           // 响应数据
    size_t size;          // 数据大小
    size_t capacity;      // 缓冲区容量
} UploadResponse;

// 上传结果结构
typedef struct {
    bool success;         // 是否成功
    int http_code;        // HTTP状态码
    char *transcription;  // 识别结果文本
    char *error_message;  // 错误信息
    double upload_time;   // 上传耗时（秒）
} SpeechRecognitionResult;

// TTS播报结果结构
typedef struct {
    bool success;         // 是否成功
    int http_code;        // HTTP状态码
    char *response_text;  // 服务器响应
    char *error_message;  // 错误信息
    double request_time;  // 请求耗时（秒）
} TTSResult;

// 函数声明

/**
 * 初始化语音上传模块
 * @return 0成功，-1失败
 */
int speech_upload_init(void);

/**
 * 清理语音上传模块
 */
void speech_upload_cleanup(void);

/**
 * 上传PCM文件进行语音识别
 * @param pcm_file_path PCM文件路径
 * @param server_url 服务器URL（NULL使用默认）
 * @return 识别结果结构体，需要调用speech_result_free释放
 */
SpeechRecognitionResult* speech_upload_pcm_file(const char *pcm_file_path, const char *server_url);

/**
 * 上传音频数据进行语音识别
 * @param audio_data 音频数据
 * @param data_size 数据大小（字节）
 * @param sample_rate 采样率
 * @param channels 声道数
 * @param server_url 服务器URL（NULL使用默认）
 * @return 识别结果结构体，需要调用speech_result_free释放
 */
SpeechRecognitionResult* speech_upload_audio_data(const int16_t *audio_data, size_t data_size,
                                                 int sample_rate, int channels, 
                                                 const char *server_url);

/**
 * 释放识别结果
 * @param result 识别结果结构体
 */
void speech_result_free(SpeechRecognitionResult *result);

/**
 * 打印识别结果
 * @param result 识别结果结构体
 */
void speech_result_print(const SpeechRecognitionResult *result);

/**
 * 设置默认服务器URL
 * @param url 服务器URL
 */
void speech_upload_set_server_url(const char *url);

/**
 * 获取当前服务器URL
 * @return 服务器URL
 */
const char* speech_upload_get_server_url(void);

/**
 * 使用阿里云实时语音识别处理音频数据
 * @param audio_data 音频数据
 * @param data_size 数据大小（字节）
 * @param sample_rate 采样率
 * @param channels 声道数
 * @return 识别结果结构体，需要调用speech_result_free释放
 */
SpeechRecognitionResult* speech_upload_audio_data_ali(const int16_t *audio_data, size_t data_size,
                                                     int sample_rate, int channels);

// TTS相关函数

/**
 * 发送文本到TTS服务器进行播报
 * @param text 要播报的文本
 * @param immediate 是否立即播报（true=立即，false=排队）
 * @param server_url TTS服务器URL（NULL使用默认）
 * @return TTS结果结构体，需要调用tts_result_free释放
 */
TTSResult* tts_speak_text(const char *text, bool immediate, const char *server_url);

/**
 * 释放TTS结果
 * @param result TTS结果结构体
 */
void tts_result_free(TTSResult *result);

/**
 * 打印TTS结果
 * @param result TTS结果结构体
 */
void tts_result_print(const TTSResult *result);

/**
 * 设置默认TTS服务器URL
 * @param url TTS服务器URL
 */
void tts_set_server_url(const char *url);

/**
 * 获取当前TTS服务器URL
 * @return TTS服务器URL
 */
const char* tts_get_server_url(void);

/**
 * 从Whisper JSON响应中提取纯文本
 * @param json_str JSON字符串
 * @return 提取的文本（需要free），失败返回NULL
 */
char* extract_text_from_whisper_json(const char *json_str);

/**
 * 将PCM数据转换为OPUS文件（16kHz单声道）
 * @param pcm_data PCM音频数据
 * @param pcm_size_bytes PCM数据大小（字节）
 * @param sample_rate 采样率
 * @param channels 声道数
 * @param opus_filename 输出OPUS文件名
 * @return 0成功，-1失败
 */
int convert_pcm_to_opus_file(const int16_t *pcm_data, size_t pcm_size_bytes,
                            int sample_rate, int channels, const char *opus_filename);

/**
 * 将PCM数据转换为OPUS数据（内存中）
 * @param pcm_data PCM音频数据
 * @param pcm_size_bytes PCM数据大小（字节）
 * @param sample_rate 采样率
 * @param channels 声道数
 * @param opus_buffer 输出OPUS数据缓冲区
 * @param opus_buffer_size OPUS缓冲区大小
 * @param opus_size 实际OPUS数据大小
 * @return 0成功，-1失败
 */
int convert_audio_to_opus_data(const int16_t *pcm_data, size_t pcm_size_bytes,
                              int sample_rate, int channels,
                              uint8_t *opus_buffer, size_t opus_buffer_size, size_t *opus_size);

/**
 * 清理语音上传模块资源
 */
void speech_upload_cleanup(void);

#endif // SPEECH_UPLOAD_H

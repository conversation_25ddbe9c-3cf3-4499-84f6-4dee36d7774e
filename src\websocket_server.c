#include "websocket_server.h"
#include "logger.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <json-c/json.h>
#include <unistd.h>

// 全局WebSocket上下文，用于广播消息
static struct lws_context *g_ws_context = NULL;
static struct lws *g_connected_clients[10]; // 最多支持10个客户端
static int g_client_count = 0;

// 音频流控制
static int g_audio_streaming_enabled = 0;  // 音频流是否启用
static int g_audio_stream_clients = 0;     // 请求音频流的客户端数量

// 外部变量声明
extern int g_auto_adjust_enabled;          // 自动调整是否启用

/**
 * WebSocket协议回调函数 - 处理AEC控制
 */
int callback_aec_control(struct lws *wsi, enum lws_callback_reasons reason,
                        void *user, void *in, size_t len) {
    char response[MAX_WS_MESSAGE_SIZE];
    unsigned char buf[LWS_PRE + MAX_WS_MESSAGE_SIZE];
    unsigned char *p = &buf[LWS_PRE];
    int response_len;

    switch (reason) {
        case LWS_CALLBACK_ESTABLISHED:
            // 新客户端连接
            if (g_client_count < 10) {
                g_connected_clients[g_client_count++] = wsi;
                LOG_INFO(MODULE_AUDIO_REALTIME, "🔗 WebSocket client connected (total: %d)", g_client_count);
                
                // 发送当前AEC延时状态
                snprintf(response, sizeof(response),
                        "{\"type\":\"aec_delay_notify\",\"delay_ms\":%d,\"status\":\"connected\"}",
                        g_aec_reference_delay_ms);
                response_len = strlen(response);
                memcpy(p, response, response_len);
                lws_write(wsi, p, response_len, LWS_WRITE_TEXT);
            }
            break;

        case LWS_CALLBACK_CLOSED:
            // 客户端断开连接
            for (int i = 0; i < g_client_count; i++) {
                if (g_connected_clients[i] == wsi) {
                    // 移除断开的客户端
                    for (int j = i; j < g_client_count - 1; j++) {
                        g_connected_clients[j] = g_connected_clients[j + 1];
                    }
                    g_client_count--;

                    // 如果这个客户端正在接收音频流，减少计数
                    if (g_audio_stream_clients > 0) {
                        g_audio_stream_clients--;
                        if (g_audio_stream_clients == 0) {
                            g_audio_streaming_enabled = 0;
                            LOG_INFO(MODULE_AUDIO_REALTIME, "🔇 Audio streaming stopped (client disconnected)");
                        }
                    }
                    break;
                }
            }
            LOG_INFO(MODULE_AUDIO_REALTIME, "❌ WebSocket client disconnected (total: %d, streaming clients: %d)",
                     g_client_count, g_audio_stream_clients);
            break;

        case LWS_CALLBACK_RECEIVE:
            // 接收到客户端消息
            {
                char *message = (char *)in;
                message[len] = '\0'; // 确保字符串结束
                
                LOG_INFO(MODULE_AUDIO_REALTIME, "📨 WebSocket received: %s", message);
                
                // 解析JSON消息
                json_object *root = json_tokener_parse(message);
                if (root != NULL) {
                    json_object *type_obj, *data_obj;
                    
                    if (json_object_object_get_ex(root, "type", &type_obj)) {
                        const char *msg_type = json_object_get_string(type_obj);
                        
                        if (strcmp(msg_type, "get_aec_delay") == 0) {
                            // 获取当前AEC延时
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"aec_delay_response\",\"delay_ms\":%d,\"status\":\"success\"}",
                                    g_aec_reference_delay_ms);
                            
                        } else if (strcmp(msg_type, "set_aec_delay") == 0) {
                            // 设置AEC延时
                            if (json_object_object_get_ex(root, "delay_ms", &data_obj)) {
                                int new_delay = json_object_get_int(data_obj);
                                
                                if (new_delay >= 1 && new_delay <= 200) {
                                    int old_delay = g_aec_reference_delay_ms;
                                    g_aec_reference_delay_ms = new_delay;
                                    
                                    LOG_INFO(MODULE_AUDIO_REALTIME, 
                                            "🔄 WebSocket AEC delay changed: %d ms → %d ms", 
                                            old_delay, new_delay);
                                    
                                    snprintf(response, sizeof(response),
                                            "{\"type\":\"aec_delay_response\",\"old_delay_ms\":%d,\"new_delay_ms\":%d,\"status\":\"success\"}",
                                            old_delay, new_delay);
                                    
                                    // 广播变化到所有客户端
                                    websocket_broadcast_aec_delay_change(old_delay, new_delay);
                                } else {
                                    snprintf(response, sizeof(response),
                                            "{\"type\":\"aec_delay_response\",\"status\":\"error\",\"message\":\"Invalid delay range (5-200ms)\"}");
                                }
                            } else {
                                snprintf(response, sizeof(response),
                                        "{\"type\":\"aec_delay_response\",\"status\":\"error\",\"message\":\"Missing delay_ms parameter\"}");
                            }
                            
                        } else if (strcmp(msg_type, "get_auto_adjust") == 0) {
                            // 获取自动调整状态
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"auto_adjust_response\",\"enabled\":%s,\"status\":\"success\"}",
                                    g_auto_adjust_enabled ? "true" : "false");

                        } else if (strcmp(msg_type, "set_auto_adjust") == 0) {
                            // 设置自动调整
                            if (json_object_object_get_ex(root, "enabled", &data_obj)) {
                                int new_enabled = json_object_get_boolean(data_obj);
                                int old_enabled = g_auto_adjust_enabled;
                                g_auto_adjust_enabled = new_enabled;

                                LOG_INFO(MODULE_AUDIO_REALTIME,
                                        "🤖 WebSocket auto-adjust changed: %s → %s",
                                        old_enabled ? "enabled" : "disabled",
                                        new_enabled ? "enabled" : "disabled");

                                snprintf(response, sizeof(response),
                                        "{\"type\":\"auto_adjust_response\",\"old_enabled\":%s,\"new_enabled\":%s,\"status\":\"success\"}",
                                        old_enabled ? "true" : "false",
                                        new_enabled ? "true" : "false");

                                // 广播变化到所有客户端
                                websocket_broadcast_auto_adjust_change(old_enabled, new_enabled);
                            } else {
                                snprintf(response, sizeof(response),
                                        "{\"type\":\"auto_adjust_response\",\"status\":\"error\",\"message\":\"Missing enabled parameter\"}");
                            }

                        } else if (strcmp(msg_type, "get_audio_status") == 0) {
                            // 获取音频状态
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"audio_status_response\",\"status\":\"running\",\"aec_delay_ms\":%d,\"auto_adjust_enabled\":%s,\"audio_streaming\":%s}",
                                    g_aec_reference_delay_ms, g_auto_adjust_enabled ? "true" : "false", g_audio_streaming_enabled ? "true" : "false");

                        } else if (strcmp(msg_type, "start_audio_stream") == 0) {
                            // 开始音频流
                            g_audio_stream_clients++;
                            if (g_audio_stream_clients == 1) {
                                g_audio_streaming_enabled = 1;
                                LOG_INFO(MODULE_AUDIO_REALTIME, "🎵 Audio streaming started (enabled=%d)", g_audio_streaming_enabled);
                            }
                            LOG_INFO(MODULE_AUDIO_REALTIME, "📡 Client requested audio stream (total clients: %d)", g_audio_stream_clients);
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"audio_stream_response\",\"status\":\"started\",\"clients\":%d}",
                                    g_audio_stream_clients);

                        } else if (strcmp(msg_type, "stop_audio_stream") == 0) {
                            // 停止音频流
                            if (g_audio_stream_clients > 0) {
                                g_audio_stream_clients--;
                                if (g_audio_stream_clients == 0) {
                                    g_audio_streaming_enabled = 0;
                                    LOG_INFO(MODULE_AUDIO_REALTIME, "🔇 Audio streaming stopped");
                                }
                            }
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"audio_stream_response\",\"status\":\"stopped\",\"clients\":%d}",
                                    g_audio_stream_clients);

                        } else {
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"error\",\"message\":\"Unknown message type: %s\"}", msg_type);
                        }
                    } else {
                        snprintf(response, sizeof(response),
                                "{\"type\":\"error\",\"message\":\"Missing type field\"}");
                    }
                    
                    json_object_put(root);
                } else {
                    snprintf(response, sizeof(response),
                            "{\"type\":\"error\",\"message\":\"Invalid JSON format\"}");
                }
                
                // 发送响应
                response_len = strlen(response);
                memcpy(p, response, response_len);
                lws_write(wsi, p, response_len, LWS_WRITE_TEXT);
            }
            break;

        default:
            break;
    }
    return 0;
}

// WebSocket协议定义
static struct lws_protocols protocols[] = {
    {
        .name = "aec-control-protocol",
        .callback = callback_aec_control,
        .per_session_data_size = 0,
        .rx_buffer_size = MAX_WS_MESSAGE_SIZE,
    },
    { NULL, NULL, 0, 0 } // 必须以空终结
};

/**
 * WebSocket服务器线程函数
 */
void *websocket_server_thread(void *arg) {
    WebSocketServer *server = (WebSocketServer *)arg;
    
    LOG_INFO(MODULE_AUDIO_REALTIME, "🚀 WebSocket server thread started");
    
    while (server->running) {
        lws_service(server->context, 1000); // 1秒超时
    }
    
    LOG_INFO(MODULE_AUDIO_REALTIME, "🛑 WebSocket server thread stopped");
    return NULL;
}

/**
 * 初始化WebSocket服务器
 */
int websocket_server_init(WebSocketServer *server, int port) {
    if (!server) return -1;
    
    memset(server, 0, sizeof(WebSocketServer));
    server->port = port;
    server->running = 0;
    
    LOG_INFO(MODULE_AUDIO_REALTIME, "WebSocket server initialized on port %d", port);
    return 0;
}

/**
 * 启动WebSocket服务器
 */
int websocket_server_start(WebSocketServer *server) {
    if (!server || server->running) return -1;
    
    struct lws_context_creation_info info;
    memset(&info, 0, sizeof(info));
    info.port = server->port;
    info.protocols = protocols;
    info.gid = -1;
    info.uid = -1;
    
    server->context = lws_create_context(&info);
    if (!server->context) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to create WebSocket context on port %d", server->port);
        return -1;
    }
    
    g_ws_context = server->context; // 保存全局上下文用于广播
    server->running = 1;
    
    // 创建WebSocket服务线程
    if (pthread_create(&server->thread, NULL, websocket_server_thread, server) != 0) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to create WebSocket server thread");
        lws_context_destroy(server->context);
        server->context = NULL;
        server->running = 0;
        return -1;
    }
    
    LOG_INFO(MODULE_AUDIO_REALTIME, "🌐 WebSocket server started on port %d", server->port);
    LOG_INFO(MODULE_AUDIO_REALTIME, "📡 WebSocket protocol: aec-control-protocol");
    LOG_INFO(MODULE_AUDIO_REALTIME, "🔗 Connect: ws://localhost:%d", server->port);
    
    return 0;
}

/**
 * 停止WebSocket服务器
 */
void websocket_server_stop(WebSocketServer *server) {
    if (!server || !server->running) return;
    
    server->running = 0;
    
    // 等待线程结束
    if (server->thread) {
        pthread_join(server->thread, NULL);
        server->thread = 0;
    }
    
    // 销毁上下文
    if (server->context) {
        lws_context_destroy(server->context);
        server->context = NULL;
    }
    
    g_ws_context = NULL;
    g_client_count = 0;
    
    LOG_INFO(MODULE_AUDIO_REALTIME, "WebSocket server stopped");
}

/**
 * 清理WebSocket服务器
 */
void websocket_server_cleanup(WebSocketServer *server) {
    if (!server) return;
    
    websocket_server_stop(server);
    LOG_INFO(MODULE_AUDIO_REALTIME, "WebSocket server cleaned up");
}

/**
 * 广播AEC延时变化到所有客户端
 */
void websocket_broadcast_aec_delay_change(int old_delay, int new_delay) {
    if (!g_ws_context || g_client_count == 0) return;
    
    char message[MAX_WS_MESSAGE_SIZE];
    snprintf(message, sizeof(message),
             "{\"type\":\"aec_delay_notify\",\"old_delay_ms\":%d,\"new_delay_ms\":%d}",
             old_delay, new_delay);
    
    unsigned char buf[LWS_PRE + MAX_WS_MESSAGE_SIZE];
    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);
    
    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }
    
    LOG_INFO(MODULE_AUDIO_REALTIME, "📡 WebSocket broadcast: AEC delay change to %d clients", g_client_count);
}

/**
 * 广播自动调整状态变化到所有客户端
 */
void websocket_broadcast_auto_adjust_change(int old_enabled, int new_enabled) {
    if (!g_ws_context || g_client_count == 0) return;

    char message[MAX_WS_MESSAGE_SIZE];
    snprintf(message, sizeof(message),
             "{\"type\":\"auto_adjust_notify\",\"old_enabled\":%s,\"new_enabled\":%s}",
             old_enabled ? "true" : "false", new_enabled ? "true" : "false");

    unsigned char buf[LWS_PRE + MAX_WS_MESSAGE_SIZE];
    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "📡 WebSocket broadcast: Auto-adjust change to %d clients", g_client_count);
}

/**
 * 广播音频状态到所有客户端
 */
void websocket_broadcast_audio_status(const char *status) {
    if (!g_ws_context || g_client_count == 0) return;

    char message[MAX_WS_MESSAGE_SIZE];
    snprintf(message, sizeof(message),
             "{\"type\":\"audio_status_notify\",\"status\": \"%s\",\"aec_delay_ms\":%d}",
             status, g_aec_reference_delay_ms);

    unsigned char buf[LWS_PRE + MAX_WS_MESSAGE_SIZE];
    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "📡 WebSocket broadcast: Audio status to %d clients", g_client_count);
}

/**
 * 发送PCM音频数据到浏览器
 */
void websocket_send_audio_data(const uint8_t *pcm_data, size_t data_size,
                              int sample_rate, int channels) {
    // 添加调试信息
    static int debug_count = 0;
    debug_count++;
    if (debug_count % 50 == 1) {  // 每50次调用打印一次状态
        LOG_INFO(MODULE_AUDIO_REALTIME, "🎵 Audio send check: context=%p, clients=%d, streaming=%d",
                 (void*)g_ws_context, g_client_count, g_audio_streaming_enabled);
    }

    if (!g_ws_context || g_client_count == 0 || !g_audio_streaming_enabled) {
        if (debug_count % 50 == 1) {
            LOG_INFO(MODULE_AUDIO_REALTIME, "❌ Audio send blocked: context=%s, clients=%s, streaming=%s",
                     g_ws_context ? "OK" : "NULL",
                     g_client_count > 0 ? "OK" : "NONE",
                     g_audio_streaming_enabled ? "ON" : "OFF");
        }
        return;
    }

    // 将PCM数据转换为Base64编码（简化传输）
    // 这里使用简单的方法，实际项目中可能需要更高效的编码
    static char base64_chars[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

    // 计算Base64编码后的大小
    size_t encoded_size = ((data_size + 2) / 3) * 4;

    // 限制数据大小，避免WebSocket消息过大
    if (encoded_size > MAX_WS_MESSAGE_SIZE / 2) {
        static int skip_count = 0;
        skip_count++;
        if (skip_count % 10 == 1) {
            LOG_INFO(MODULE_AUDIO_REALTIME, "⚠️ Audio data too large: %zu bytes (encoded: %zu), max: %d (skipped: %d times)",
                     data_size, encoded_size, MAX_WS_MESSAGE_SIZE / 2, skip_count);
        }
        return; // 数据太大，跳过
    }

    char *encoded_data = malloc(encoded_size + 1);
    if (!encoded_data) return;

    // 简单的Base64编码
    size_t encoded_len = 0;
    for (size_t i = 0; i < data_size; i += 3) {
        uint32_t octet_a = i < data_size ? pcm_data[i] : 0;
        uint32_t octet_b = i + 1 < data_size ? pcm_data[i + 1] : 0;
        uint32_t octet_c = i + 2 < data_size ? pcm_data[i + 2] : 0;

        uint32_t triple = (octet_a << 0x10) + (octet_b << 0x08) + octet_c;

        encoded_data[encoded_len++] = base64_chars[(triple >> 3 * 6) & 0x3F];
        encoded_data[encoded_len++] = base64_chars[(triple >> 2 * 6) & 0x3F];
        encoded_data[encoded_len++] = base64_chars[(triple >> 1 * 6) & 0x3F];
        encoded_data[encoded_len++] = base64_chars[(triple >> 0 * 6) & 0x3F];
    }

    // 处理填充
    if (data_size % 3 == 1) {
        encoded_data[encoded_len - 1] = '=';
        encoded_data[encoded_len - 2] = '=';
    } else if (data_size % 3 == 2) {
        encoded_data[encoded_len - 1] = '=';
    }

    encoded_data[encoded_len] = '\0';

    // 创建JSON消息
    char message[MAX_WS_MESSAGE_SIZE];
    int message_len = snprintf(message, sizeof(message),
                              "{\"type\":\"audio_data\",\"sample_rate\":%d,\"channels\":%d,\"data\":\"%s\"}",
                              sample_rate, channels, encoded_data);

    free(encoded_data);

    if (message_len >= MAX_WS_MESSAGE_SIZE) {
        LOG_INFO(MODULE_AUDIO_REALTIME, "⚠️ Final message too large: %d bytes, max: %d",
                 message_len, MAX_WS_MESSAGE_SIZE);
        return; // 消息太大
    }

    unsigned char buf[LWS_PRE + MAX_WS_MESSAGE_SIZE];
    unsigned char *p = &buf[LWS_PRE];
    memcpy(p, message, message_len);

    // 发送到所有连接的客户端
    int sent_count = 0;
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            int result = lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
            if (result >= 0) {
                sent_count++;
            }
        }
    }

    // 每100次发送打印一次统计
    static int send_count = 0;
    send_count++;
    if (send_count % 100 == 1) {
        LOG_INFO(MODULE_AUDIO_REALTIME, "📤 Audio data sent: %d bytes to %d clients (total sends: %d)",
                 message_len, sent_count, send_count);
    }
}

/**
 * 广播音频流控制消息
 */
void websocket_broadcast_audio_stream_control(const char *action) {
    if (!g_ws_context || g_client_count == 0) return;

    char message[MAX_WS_MESSAGE_SIZE];
    snprintf(message, sizeof(message),
             "{\"type\":\"audio_stream_notify\",\"action\":\"%s\",\"clients\":%d}",
             action, g_audio_stream_clients);

    unsigned char buf[LWS_PRE + MAX_WS_MESSAGE_SIZE];
    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }
}

<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{e4c0125c-b67b-4fbf-aa62-1f68ba64e1ea}</ProjectGuid>
    <RootNamespace>speechRecognizerDemo</RootNamespace>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <SupportLTL>false</SupportLTL>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <SupportLTL>false</SupportLTL>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <SupportLTL>false</SupportLTL>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <SupportLTL>false</SupportLTL>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)..\..\build\build_win32\nlsCppSdk\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)..\..\build\build_win32\nlsCppSdk\$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)..\..\build\build_win32\nlsCppSdk\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)..\..\build\build_win32\nlsCppSdk\$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)..\..\build\build_win64\nlsCppSdk\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)..\..\build\build_win64\nlsCppSdk\$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)..\..\build\build_win64\nlsCppSdk\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)..\..\build\build_win64\nlsCppSdk\$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>false</SDLCheck>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;HAVE_STRUCT_TIMESPEC;WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>$(SolutionDir)..\..\build\build_win32\thirdparty\curl-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\libevent-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\log4cpp-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\jsoncpp-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\openssl-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\opus-prefix\include;$(SolutionDir)..\framework\feature;$(SolutionDir)..\framework\feature\da;$(SolutionDir)..\framework\feature\sy;$(SolutionDir)..\framework\feature\st;$(SolutionDir)..\framework\feature\sr;$(SolutionDir)..\framework\item;$(SolutionDir)..\framework\common;$(SolutionDir)..\token\include\internal;$(SolutionDir)..\token\include;$(SolutionDir)..\utils;$(SolutionDir)..\transport;$(SolutionDir)..\event;$(SolutionDir)..\encoder;$(SolutionDir)include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(SolutionDir)libs_14.0\x86\pthread;$(SolutionDir)libs_14.0\x86\Debug;$(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Debug;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>nlsCppSdk.lib;pthreadVC2.lib;libcrypto-1_1.lib;libssl-1_1.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PostBuildEvent>
      <Command>copy $(SolutionDir)libs_14.0\x86\pthread\pthreadVC2.lib  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Debug
copy $(SolutionDir)libs_14.0\x86\pthread\pthreadVC2.dll  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Debug
copy $(SolutionDir)libs_14.0\x86\Debug\libcrypto-1_1-x64.lib  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Debug
copy $(SolutionDir)libs_14.0\x86\Debug\libcrypto-1_1-x64.dll  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Debug
copy $(SolutionDir)libs_14.0\x86\Debug\libssl-1_1-x64.lib  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Debug
copy $(SolutionDir)libs_14.0\x86\Debug\libssl-1_1-x64.dll  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Debug
copy $(SolutionDir)libs_14.0\x86\Debug\libcurld.lib  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Debug
copy $(SolutionDir)libs_14.0\x86\Debug\libcurld.dll  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Debug
copy $(SolutionDir)libs_14.0\x86\Debug\libeay32.lib  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Debug
copy $(SolutionDir)libs_14.0\x86\Debug\libeay32.dll  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Debug
copy $(SolutionDir)libs_14.0\x86\Debug\ssleay32.lib  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Debug
copy $(SolutionDir)libs_14.0\x86\Debug\ssleay32.dll  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Debug</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;HAVE_STRUCT_TIMESPEC;WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>$(SolutionDir)..\..\build\build_win32\thirdparty\curl-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\libevent-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\log4cpp-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\jsoncpp-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\openssl-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\opus-prefix\include;$(SolutionDir)..\framework\feature;$(SolutionDir)..\framework\feature\da;$(SolutionDir)..\framework\feature\sy;$(SolutionDir)..\framework\feature\st;$(SolutionDir)..\framework\feature\sr;$(SolutionDir)..\framework\item;$(SolutionDir)..\framework\common;$(SolutionDir)..\token\include\internal;$(SolutionDir)..\token\include;$(SolutionDir)..\utils;$(SolutionDir)..\transport;$(SolutionDir)..\event;$(SolutionDir)..\encoder;$(SolutionDir)include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(SolutionDir)libs_14.0\x86\pthread;$(SolutionDir)libs_14.0\x86\Release;$(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Release;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>nlsCppSdk.lib;pthreadVC2.lib;libcrypto-1_1.lib;libssl-1_1.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PostBuildEvent>
      <Command>copy $(SolutionDir)libs_14.0\x86\pthread\pthreadVC2.lib  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Release
copy $(SolutionDir)libs_14.0\x86\pthread\pthreadVC2.dll  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Release
copy $(SolutionDir)libs_14.0\x86\Release\libcrypto-1_1.lib  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Release
copy $(SolutionDir)libs_14.0\x86\Release\libcrypto-1_1.dll  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Release
copy $(SolutionDir)libs_14.0\x86\Release\libssl-1_1.lib  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Release
copy $(SolutionDir)libs_14.0\x86\Release\libssl-1_1.dll  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Release
copy $(SolutionDir)libs_14.0\x86\Release\libcurl.lib  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Release
copy $(SolutionDir)libs_14.0\x86\Release\libcurl.dll  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Release
copy $(SolutionDir)libs_14.0\x86\Release\libeay32.lib  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Release
copy $(SolutionDir)libs_14.0\x86\Release\libeay32.dll  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Release
copy $(SolutionDir)libs_14.0\x86\Release\ssleay32.lib  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Release
copy $(SolutionDir)libs_14.0\x86\Release\ssleay32.dll  $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Release</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>false</SDLCheck>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;HAVE_STRUCT_TIMESPEC;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>$(SolutionDir)..\..\build\build_win64\thirdparty\curl-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\libevent-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\log4cpp-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\jsoncpp-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\openssl-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\opus-prefix\include;$(SolutionDir)..\framework\feature;$(SolutionDir)..\framework\feature\da;$(SolutionDir)..\framework\feature\sy;$(SolutionDir)..\framework\feature\st;$(SolutionDir)..\framework\feature\sr;$(SolutionDir)..\framework\item;$(SolutionDir)..\framework\common;$(SolutionDir)..\token\include\internal;$(SolutionDir)..\token\include;$(SolutionDir)..\utils;$(SolutionDir)..\transport;$(SolutionDir)..\event;$(SolutionDir)..\encoder;$(SolutionDir)include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(SolutionDir)libs_14.0\x64\pthread;$(SolutionDir)libs_14.0\x64\Debug;$(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Debug;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>nlsCppSdk.lib;pthreadVC2.lib;libcrypto-1_1-x64.lib;libssl-1_1-x64.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PostBuildEvent>
      <Command>copy $(SolutionDir)libs_14.0\x64\pthread\pthreadVC2.lib  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Debug
copy $(SolutionDir)libs_14.0\x64\pthread\pthreadVC2.dll  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Debug
copy $(SolutionDir)libs_14.0\x64\Debug\libcrypto-1_1-x64.lib  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Debug
copy $(SolutionDir)libs_14.0\x64\Debug\libcrypto-1_1-x64.dll  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Debug
copy $(SolutionDir)libs_14.0\x64\Debug\libssl-1_1-x64.lib  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Debug
copy $(SolutionDir)libs_14.0\x64\Debug\libssl-1_1-x64.dll  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Debug
copy $(SolutionDir)libs_14.0\x64\Debug\libcurld.lib  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Debug
copy $(SolutionDir)libs_14.0\x64\Debug\libcurld.dll  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Debug
copy $(SolutionDir)libs_14.0\x64\Debug\libeay32.lib  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Debug
copy $(SolutionDir)libs_14.0\x64\Debug\libeay32.dll  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Debug
copy $(SolutionDir)libs_14.0\x64\Debug\ssleay32.lib  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Debug
copy $(SolutionDir)libs_14.0\x64\Debug\ssleay32.dll  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Debug</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>false</SDLCheck>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;HAVE_STRUCT_TIMESPEC;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>$(SolutionDir)..\..\build\build_win64\thirdparty\curl-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\libevent-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\log4cpp-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\jsoncpp-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\openssl-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\opus-prefix\include;$(SolutionDir)..\framework\feature;$(SolutionDir)..\framework\feature\da;$(SolutionDir)..\framework\feature\sy;$(SolutionDir)..\framework\feature\st;$(SolutionDir)..\framework\feature\sr;$(SolutionDir)..\framework\item;$(SolutionDir)..\framework\common;$(SolutionDir)..\token\include\internal;$(SolutionDir)..\token\include;$(SolutionDir)..\utils;$(SolutionDir)..\transport;$(SolutionDir)..\event;$(SolutionDir)..\encoder;$(SolutionDir)include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(SolutionDir)libs_14.0\x64\pthread;$(SolutionDir)libs_14.0\x64\Release;$(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Release;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>nlsCppSdk.lib;pthreadVC2.lib;libcrypto-1_1-x64.lib;libssl-1_1-x64.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PostBuildEvent>
      <Command>copy $(SolutionDir)libs_14.0\x64\pthread\pthreadVC2.lib  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Release
copy $(SolutionDir)libs_14.0\x64\pthread\pthreadVC2.dll  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Release
copy $(SolutionDir)libs_14.0\x64\Release\libcrypto-1_1-x64.lib  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Release
copy $(SolutionDir)libs_14.0\x64\Release\libcrypto-1_1-x64.dll  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Release
copy $(SolutionDir)libs_14.0\x64\Release\libssl-1_1-x64.lib  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Release
copy $(SolutionDir)libs_14.0\x64\Release\libssl-1_1-x64.dll  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Release
copy $(SolutionDir)libs_14.0\x64\Release\libcurl.lib  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Release
copy $(SolutionDir)libs_14.0\x64\Release\libcurl.dll  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Release
copy $(SolutionDir)libs_14.0\x64\Release\libeay32.lib  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Release
copy $(SolutionDir)libs_14.0\x64\Release\libeay32.dll  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Release
copy $(SolutionDir)libs_14.0\x64\Release\ssleay32.lib  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Release
copy $(SolutionDir)libs_14.0\x64\Release\ssleay32.dll  $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Release</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\demo\Windows\speechRecognizerDemo.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets" />
</Project>
//
// Created by liang on 2025/7/20.
//

#include "audio_covert.h"
#include "app_resampler.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <sys/stat.h>
#include <unistd.h>
#include <ctype.h>
#include <opus/opus.h>
#include <ogg/ogg.h>
#include "logger.h"


/**
 * 使用SpeexDSP进行高质量重采样和声道转换
 */
static int16_t* convert_audio_format_speex(const int16_t *input_data, size_t input_samples,
                                           int input_rate, int input_channels,
                                           int output_rate, int output_channels,
                                           size_t *output_samples) {
    if (!input_data || input_samples == 0) return NULL;

    LOG_INFO(MODULE_SPEECH_UPLOAD, "🔄 SpeexDSP Converting: %dHz %dch → %dHz %dch (%zu samples)",
             input_rate, input_channels, output_rate, output_channels, input_samples);

    // 计算输出缓冲区大小（预估，稍微大一些）
    size_t estimated_output = (input_samples * output_rate / input_rate) + 1024;
    int16_t *output_data = malloc(estimated_output * sizeof(int16_t));
    if (!output_data) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Error: Failed to allocate output buffer");
        return NULL;
    }

    // 使用应用重采样器
    int actual_output_samples;
    if (app_resample_audio(input_data, (int)input_samples, input_rate, input_channels,
                           output_data, (int)estimated_output, output_rate, output_channels,
                           &actual_output_samples) != 0) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "Error: SpeexDSP resampling failed");
        free(output_data);
        return NULL;
    }

    *output_samples = (size_t)actual_output_samples;

    LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ SpeexDSP conversion successful: %zu → %zu samples",
             input_samples, *output_samples);

    return output_data;
}


/**
 * 创建标准的OpusHead头数据包
 */
static int create_opus_header(unsigned char *header_data, int sample_rate, int channels) {
    memset(header_data, 0, 19);  // 清零所有字段

    // OpusHead标识符（8字节）
    memcpy(header_data, "OpusHead", 8);

    // 版本号（1字节）
    header_data[8] = 1;

    // 声道数（1字节）
    header_data[9] = (unsigned char)channels;

    // 预跳过样本数（16位小端）- 根据采样率调整
    int pre_skip = (sample_rate == 16000) ? 104 : 312;  // 16kHz: 104, 48kHz: 312
    header_data[10] = pre_skip & 0xFF;
    header_data[11] = (pre_skip >> 8) & 0xFF;

    // 原始采样率（32位小端）
    header_data[12] = sample_rate & 0xFF;
    header_data[13] = (sample_rate >> 8) & 0xFF;
    header_data[14] = (sample_rate >> 16) & 0xFF;
    header_data[15] = (sample_rate >> 24) & 0xFF;

    // 输出增益（16位小端）- 0 dB
    header_data[16] = 0;
    header_data[17] = 0;

    // 声道映射族（1字节）- 0表示单声道/立体声
    header_data[18] = 0;

    return 19;  // OpusHead头部大小
}

/**
 * 创建OpusTags数据包
 */
static int create_opus_tags(unsigned char *tags_data) {
    const char *vendor = "libopus";
    const char *comment = "Encoded by mic_dev";

    int pos = 0;

    // OpusTags标识符（8字节）
    memcpy(tags_data + pos, "OpusTags", 8);
    pos += 8;

    // Vendor字符串长度（32位小端）
    int vendor_len = strlen(vendor);
    tags_data[pos++] = vendor_len & 0xFF;
    tags_data[pos++] = (vendor_len >> 8) & 0xFF;
    tags_data[pos++] = (vendor_len >> 16) & 0xFF;
    tags_data[pos++] = (vendor_len >> 24) & 0xFF;

    // Vendor字符串
    memcpy(tags_data + pos, vendor, vendor_len);
    pos += vendor_len;

    // 用户注释数量（32位小端）- 1个注释
    tags_data[pos++] = 1;
    tags_data[pos++] = 0;
    tags_data[pos++] = 0;
    tags_data[pos++] = 0;

    // 注释长度（32位小端）
    int comment_len = strlen(comment);
    tags_data[pos++] = comment_len & 0xFF;
    tags_data[pos++] = (comment_len >> 8) & 0xFF;
    tags_data[pos++] = (comment_len >> 16) & 0xFF;
    tags_data[pos++] = (comment_len >> 24) & 0xFF;

    // 注释字符串
    memcpy(tags_data + pos, comment, comment_len);
    pos += comment_len;

    return pos;  // 总大小
}

/**
 * 将PCM数据转换为16kHz单声道Opus格式，并通过指针返回包含Ogg容器的字节数组
 */
int convert_pcm_to_opus_bytes(const int16_t *pcm_data, size_t pcm_size_bytes,
                              int sample_rate, int channels,
                              unsigned char **opus_data_ptr, size_t *opus_size) {
    LOG_INFO(MODULE_SPEECH_UPLOAD, "🔄 Converting PCM to 16kHz mono Opus bytes: %dHz, %dch, %.2f KB",
             sample_rate, channels, pcm_size_bytes / 1024.0);

    *opus_data_ptr = NULL;
    *opus_size = 0;

    // 调试：打印PCM数据的前几个样本
    if (pcm_size_bytes >= 16) {
        const int16_t *samples = (const int16_t*)pcm_data;
        LOG_INFO(MODULE_SPEECH_UPLOAD, "🔍 PCM first 8 samples: [%d,%d,%d,%d,%d,%d,%d,%d]",
                 samples[0], samples[1], samples[2], samples[3],
                 samples[4], samples[5], samples[6], samples[7]);
    }

    // 步骤1：转换为16kHz单声道
    size_t input_samples = pcm_size_bytes / sizeof(int16_t);
    size_t output_samples = 0;
    int16_t *resampled_data = NULL;

    if (sample_rate != 16000 || channels != 1) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "🔄 Resampling %dHz %dch -> 16kHz 1ch", sample_rate, channels);
        resampled_data = convert_audio_format_speex(pcm_data, input_samples,
                                                    sample_rate, channels,
                                                    16000, 1, &output_samples);
        if (!resampled_data) {
            LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Error: Failed to resample audio");
            return -1;
        }
        LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ Resampled: %zu -> %zu samples", input_samples, output_samples);
    } else {
        resampled_data = (int16_t*)pcm_data;
        output_samples = input_samples;
        LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ Audio already in 16kHz mono format");
    }

    // 步骤2：初始化Opus编码器
    int err;
    OpusEncoder *encoder = opus_encoder_create(16000, 1, OPUS_APPLICATION_AUDIO, &err);
    if (err != OPUS_OK) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Error: Failed to create Opus encoder: %s", opus_strerror(err));
        if (resampled_data != pcm_data) free(resampled_data);
        return -1;
    }
    opus_encoder_ctl(encoder, OPUS_SET_BITRATE(OPUS_BITRATE));
    LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ Opus encoder created: 16kHz, 1ch, %d bps", OPUS_BITRATE);

    // 初始化Ogg流
    ogg_stream_state ogg_state;
    srand(time(NULL));
    ogg_stream_init(&ogg_state, rand());

    // 动态字节缓冲区
    unsigned char *local_output_buffer = NULL;
    size_t buffer_capacity = 0;
    size_t buffer_size = 0;
    auto void append_to_buffer(const void *data, size_t size) {
        if (buffer_size + size > buffer_capacity) {
            buffer_capacity = (buffer_size + size) * 2;
            local_output_buffer = realloc(local_output_buffer, buffer_capacity);
        }
        memcpy(local_output_buffer + buffer_size, data, size);
        buffer_size += size;
    };

    // 写入Ogg/Opus头
    ogg_page ogg_page;
    ogg_packet ogg_packet;
    unsigned char header_data[19];
    int header_size = create_opus_header(header_data, sample_rate, channels);
    ogg_packet.packet = header_data;
    ogg_packet.bytes = header_size;
    ogg_packet.b_o_s = 1; ogg_packet.e_o_s = 0; ogg_packet.granulepos = 0; ogg_packet.packetno = 0;
    ogg_stream_packetin(&ogg_state, &ogg_packet);

    unsigned char tags_data[256];
    int tags_size = create_opus_tags(tags_data);
    ogg_packet.packet = tags_data;
    ogg_packet.bytes = tags_size;
    ogg_packet.b_o_s = 0;
    ogg_stream_packetin(&ogg_state, &ogg_packet);

    while (ogg_stream_flush(&ogg_state, &ogg_page)) {
        append_to_buffer(ogg_page.header, ogg_page.header_len);
        append_to_buffer(ogg_page.body, ogg_page.body_len);
    }
    LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ OpusHead and OpusTags generated");

    // 分配编码缓冲区
    opus_int16 *frame_buffer = malloc(OPUS_FRAME_SIZE * 1 * sizeof(opus_int16));
    unsigned char *opus_packet_data = malloc(OPUS_MAX_PACKET_SIZE);
    if (!frame_buffer || !opus_packet_data) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Error: Memory allocation failed");
        opus_encoder_destroy(encoder); ogg_stream_clear(&ogg_state);
        if (resampled_data != pcm_data) free(resampled_data);
        free(local_output_buffer); free(frame_buffer); free(opus_packet_data);
        return -1;
    }

    // 编码循环
    size_t remaining_samples = output_samples;
    const int16_t *input_ptr = resampled_data;
    ogg_int64_t granulepos = ((sample_rate == 16000) ? 104 : 312);
    ogg_int64_t packetno = 2;
    size_t encoded_frames = 0;

    while (remaining_samples > 0) {
        size_t current_frame_samples = (remaining_samples >= OPUS_FRAME_SIZE) ? OPUS_FRAME_SIZE : remaining_samples;
        memcpy(frame_buffer, input_ptr, current_frame_samples * sizeof(int16_t));
        if (current_frame_samples < OPUS_FRAME_SIZE) {
            memset(frame_buffer + current_frame_samples, 0, (OPUS_FRAME_SIZE - current_frame_samples) * sizeof(int16_t));
        }

        int packet_size = opus_encode(encoder, frame_buffer, OPUS_FRAME_SIZE, opus_packet_data, OPUS_MAX_PACKET_SIZE);
        if (packet_size < 0) {
            LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Error: Encoding failed: %s", opus_strerror(packet_size));
            remaining_samples = 1; // Mark as failed
            break;
        }

        granulepos += ((sample_rate == 16000) ? 320 : OPUS_FRAME_SIZE);
        ogg_packet.packet = opus_packet_data;
        ogg_packet.bytes = packet_size;
        ogg_packet.e_o_s = (remaining_samples <= OPUS_FRAME_SIZE) ? 1 : 0;
        ogg_packet.granulepos = granulepos;
        ogg_packet.packetno = packetno++;
        ogg_stream_packetin(&ogg_state, &ogg_packet);

        while (ogg_stream_pageout(&ogg_state, &ogg_page)) {
            append_to_buffer(ogg_page.header, ogg_page.header_len);
            append_to_buffer(ogg_page.body, ogg_page.body_len);
        }

        input_ptr += current_frame_samples;
        remaining_samples -= current_frame_samples;
        encoded_frames++;
    }

    while (ogg_stream_flush(&ogg_state, &ogg_page)) {
        append_to_buffer(ogg_page.header, ogg_page.header_len);
        append_to_buffer(ogg_page.body, ogg_page.body_len);
    }

    // 清理
    free(frame_buffer);
    free(opus_packet_data);
    if (resampled_data != pcm_data) free(resampled_data);
    opus_encoder_destroy(encoder);
    ogg_stream_clear(&ogg_state);

    if (remaining_samples == 0) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ PCM converted to Opus bytes successfully");
        LOG_INFO(MODULE_SPEECH_UPLOAD, "📊 Encoded %zu frames, total size: %.2f KB", encoded_frames, buffer_size / 1024.0);
        *opus_data_ptr = local_output_buffer;
        *opus_size = buffer_size;
        return 0;
    } else {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Error: Encoding incomplete");
        free(local_output_buffer);
        return -1;
    }
}

/**
 * 将PCM数据转换为16kHz单声道Opus格式并保存（使用libopus + Ogg容器）
 */
int convert_pcm_to_opus_file(const int16_t *pcm_data, size_t pcm_size_bytes,
                             int sample_rate, int channels, const char *opus_filename) {
    unsigned char *opus_data = NULL;
    size_t opus_size = 0;
    if (convert_pcm_to_opus_bytes(pcm_data, pcm_size_bytes, sample_rate, channels, &opus_data, &opus_size) != 0) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Error: Failed to convert PCM to Opus bytes");
        return -1;
    }

    FILE *fout = fopen(opus_filename, "wb");
    if (!fout) {
        LOG_INFO(MODULE_SPEECH_UPLOAD, "❌ Error: Failed to create output file: %s", opus_filename);
        free(opus_data);
        return -1;
    }

    fwrite(opus_data, 1, opus_size, fout);
    fclose(fout);
    free(opus_data);

    LOG_INFO(MODULE_SPEECH_UPLOAD, "✅ Opus data successfully written to file: %s", opus_filename);
    LOG_INFO(MODULE_SPEECH_UPLOAD, "📊 Output file size: %.2f KB", opus_size / 1024.0);

    return 0;
}

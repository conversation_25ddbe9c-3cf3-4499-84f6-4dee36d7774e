#!/bin/bash

# AEC延时修复和自动调整功能测试脚本
# 测试HTTP API和WebSocket功能

echo "🔧 AEC延时修复和自动调整功能测试"
echo "=================================="

# 服务器地址
HTTP_SERVER="http://localhost:8080"
WS_SERVER="ws://localhost:9000"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查服务器是否运行
check_server() {
    echo -e "${BLUE}📡 检查服务器状态...${NC}"
    
    # 检查HTTP服务器
    if curl -s "$HTTP_SERVER/status" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ HTTP服务器运行正常${NC}"
    else
        echo -e "${RED}❌ HTTP服务器未运行或无法访问${NC}"
        return 1
    fi
    
    # 检查WebSocket服务器（通过netstat或ss）
    if command -v ss > /dev/null 2>&1; then
        if ss -ln | grep -q ":9000"; then
            echo -e "${GREEN}✅ WebSocket服务器端口9000已监听${NC}"
        else
            echo -e "${YELLOW}⚠️ WebSocket服务器端口9000未监听${NC}"
        fi
    fi
    
    return 0
}

# 测试HTTP API
test_http_api() {
    echo -e "\n${BLUE}🌐 测试HTTP API功能${NC}"
    echo "------------------------"
    
    # 获取当前状态
    echo "📊 获取当前状态:"
    response=$(curl -s "$HTTP_SERVER/status")
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
    
    # 测试延时调整
    echo -e "\n⏱️ 测试延时调整:"
    delays=(30 45 60 75 94)
    
    for delay in "${delays[@]}"; do
        echo "设置延时为 ${delay}ms..."
        response=$(curl -s -X POST "$HTTP_SERVER/set_aec_delay" \
            -H "Content-Type: application/json" \
            -d "{\"delay_ms\": $delay}")
        
        status=$(echo "$response" | jq -r '.status' 2>/dev/null)
        if [ "$status" = "success" ]; then
            echo -e "${GREEN}✅ 延时设置成功${NC}"
        else
            echo -e "${RED}❌ 延时设置失败: $response${NC}"
        fi
        
        sleep 1
    done
    
    # 测试自动调整功能
    echo -e "\n🤖 测试自动调整功能:"
    
    # 启用自动调整
    echo "启用自动调整..."
    response=$(curl -s -X POST "$HTTP_SERVER/set_auto_adjust" \
        -H "Content-Type: application/json" \
        -d '{"enabled": true}')
    
    status=$(echo "$response" | jq -r '.status' 2>/dev/null)
    if [ "$status" = "success" ]; then
        echo -e "${GREEN}✅ 自动调整启用成功${NC}"
    else
        echo -e "${RED}❌ 自动调整启用失败: $response${NC}"
    fi
    
    sleep 1
    
    # 禁用自动调整
    echo "禁用自动调整..."
    response=$(curl -s -X POST "$HTTP_SERVER/set_auto_adjust" \
        -H "Content-Type: application/json" \
        -d '{"enabled": false}')
    
    status=$(echo "$response" | jq -r '.status' 2>/dev/null)
    if [ "$status" = "success" ]; then
        echo -e "${GREEN}✅ 自动调整禁用成功${NC}"
    else
        echo -e "${RED}❌ 自动调整禁用失败: $response${NC}"
    fi
    
    # 最终状态检查
    echo -e "\n📊 最终状态检查:"
    response=$(curl -s "$HTTP_SERVER/status")
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
}

# 测试WebSocket功能（需要websocat工具）
test_websocket() {
    echo -e "\n${BLUE}🔌 测试WebSocket功能${NC}"
    echo "------------------------"
    
    if ! command -v websocat > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠️ websocat工具未安装，跳过WebSocket测试${NC}"
        echo "安装方法: cargo install websocat 或 apt install websocat"
        return 0
    fi
    
    echo "测试WebSocket消息..."
    
    # 创建临时测试文件
    cat > /tmp/ws_test_commands.json << EOF
{"type": "get_audio_status"}
{"type": "get_auto_adjust"}
{"type": "set_auto_adjust", "enabled": true}
{"type": "get_aec_delay"}
{"type": "set_aec_delay", "delay_ms": 50}
EOF
    
    # 发送测试命令
    timeout 10s websocat "$WS_SERVER" < /tmp/ws_test_commands.json > /tmp/ws_responses.json 2>/dev/null &
    WS_PID=$!
    
    sleep 2
    
    if kill -0 $WS_PID 2>/dev/null; then
        echo -e "${GREEN}✅ WebSocket连接成功${NC}"
        wait $WS_PID
        
        if [ -f /tmp/ws_responses.json ] && [ -s /tmp/ws_responses.json ]; then
            echo "WebSocket响应:"
            cat /tmp/ws_responses.json
        fi
    else
        echo -e "${RED}❌ WebSocket连接失败${NC}"
    fi
    
    # 清理临时文件
    rm -f /tmp/ws_test_commands.json /tmp/ws_responses.json
}

# 显示修复总结
show_summary() {
    echo -e "\n${BLUE}📋 修复总结${NC}"
    echo "============"
    echo ""
    echo -e "${GREEN}✅ 已修复的问题:${NC}"
    echo "1. 延时样本数计算错误（不再乘以声道数）"
    echo "2. 添加了动态延时验证和自适应调整"
    echo "3. 提供了HTTP API控制延时和自动调整"
    echo "4. 添加了WebSocket实时控制和通知"
    echo "5. 改进了延时补偿的精确性"
    echo ""
    echo -e "${BLUE}🎯 预期效果:${NC}"
    echo "- 延时补偿更精确（94ms对应4512个样本而不是9024个）"
    echo "- 系统会自动检测和调整最优延时"
    echo "- 回声消除效果应该显著改善"
    echo "- 支持实时监控和调整"
    echo ""
    echo -e "${YELLOW}📖 使用说明:${NC}"
    echo "- HTTP API: curl -X POST $HTTP_SERVER/set_aec_delay -d '{\"delay_ms\": 50}'"
    echo "- HTTP API: curl -X POST $HTTP_SERVER/set_auto_adjust -d '{\"enabled\": true}'"
    echo "- WebSocket: 打开 test_websocket_auto_adjust.html 进行交互式测试"
    echo "- 状态查询: curl $HTTP_SERVER/status"
}

# 主测试流程
main() {
    echo "开始测试..."
    
    if ! check_server; then
        echo -e "${RED}❌ 服务器检查失败，请确保音频处理程序正在运行${NC}"
        exit 1
    fi
    
    test_http_api
    test_websocket
    show_summary
    
    echo -e "\n${GREEN}🎉 测试完成！${NC}"
}

# 检查依赖
check_dependencies() {
    local missing_deps=()
    
    if ! command -v curl > /dev/null 2>&1; then
        missing_deps+=("curl")
    fi
    
    if ! command -v jq > /dev/null 2>&1; then
        missing_deps+=("jq")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo -e "${RED}❌ 缺少依赖工具: ${missing_deps[*]}${NC}"
        echo "请安装: sudo apt install ${missing_deps[*]}"
        exit 1
    fi
}

# 脚本入口
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --help, -h    显示此帮助信息"
    echo "  --http-only   仅测试HTTP API"
    echo "  --ws-only     仅测试WebSocket"
    echo ""
    echo "示例:"
    echo "  $0              # 运行完整测试"
    echo "  $0 --http-only  # 仅测试HTTP API"
    exit 0
fi

check_dependencies

case "$1" in
    --http-only)
        check_server && test_http_api
        ;;
    --ws-only)
        check_server && test_websocket
        ;;
    *)
        main
        ;;
esac

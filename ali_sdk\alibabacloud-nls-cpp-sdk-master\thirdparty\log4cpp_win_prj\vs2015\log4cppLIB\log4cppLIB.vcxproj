﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug with Boost|Win32">
      <Configuration>Debug with Boost</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug with Boost|x64">
      <Configuration>Debug with Boost</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release with Boost|Win32">
      <Configuration>Release with Boost</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release with Boost|x64">
      <Configuration>Release with Boost</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{AFA856A0-**************-CE4061709569}</ProjectGuid>
    <SccProjectName />
    <SccLocalPath />
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.40219.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(SolutionDir)$(Platform)\$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(SolutionDir)$(Platform)\$(Configuration)\</IntDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(SolutionDir)$(Platform)\$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(SolutionDir)$(Platform)\$(Configuration)\</IntDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">$(Configuration)\</IntDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">$(Configuration)\</IntDir>
    <CodeAnalysisRuleSet Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRuleSet Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'" />
    <CodeAnalysisRules Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'" />
    <CodeAnalysisRuleAssemblies Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'" />
    <CodeAnalysisRuleAssemblies Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'" />
    <CodeAnalysisRuleSet Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRuleSet Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" />
    <CodeAnalysisRules Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" />
    <CodeAnalysisRuleAssemblies Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" />
    <CodeAnalysisRuleAssemblies Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" />
    <CodeAnalysisRuleSet Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRuleSet Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'" />
    <CodeAnalysisRules Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'" />
    <CodeAnalysisRuleAssemblies Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'" />
    <CodeAnalysisRuleAssemblies Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'" />
    <CodeAnalysisRuleSet Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRuleSet Condition="'$(Configuration)|$(Platform)'=='Release|x64'">AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" />
    <CodeAnalysisRules Condition="'$(Configuration)|$(Platform)'=='Release|x64'" />
    <CodeAnalysisRuleAssemblies Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" />
    <CodeAnalysisRuleAssemblies Condition="'$(Configuration)|$(Platform)'=='Release|x64'" />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IntDir>$(SolutionDir)$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IntDir>$(SolutionDir)$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Debug/log4cppLIB.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\Debug/</AssemblerListingLocation>
      <ObjectFileName>.\Debug/</ObjectFileName>
      <ProgramDataBaseFileName>.\Debug/</ProgramDataBaseFileName>
      <BrowseInformation>true</BrowseInformation>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <Lib>
      <OutputFile>Debug\log4cppD.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Debug/log4cppLIB.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\Debug/</AssemblerListingLocation>
      <ObjectFileName>.\Debug/</ObjectFileName>
      <ProgramDataBaseFileName>.\Debug/</ProgramDataBaseFileName>
      <BrowseInformation>true</BrowseInformation>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <Lib>
      <OutputFile>Debug\log4cppD.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Release/log4cppLIB.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\Release/</AssemblerListingLocation>
      <ObjectFileName>.\Release/</ObjectFileName>
      <ProgramDataBaseFileName>.\Release/</ProgramDataBaseFileName>
      <BrowseInformation>true</BrowseInformation>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <Lib>
      <OutputFile>.\Release\log4cppLIB.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Release/log4cppLIB.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\Release/</AssemblerListingLocation>
      <ObjectFileName>.\Release/</ObjectFileName>
      <ProgramDataBaseFileName>.\Release/</ProgramDataBaseFileName>
      <BrowseInformation>true</BrowseInformation>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <Lib>
      <OutputFile>.\Release\log4cppLIB.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;LOG4CPP_HAVE_BOOST;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Debug/log4cppLIB.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\Debug/</AssemblerListingLocation>
      <ObjectFileName>.\Debug/</ObjectFileName>
      <ProgramDataBaseFileName>.\Debug/</ProgramDataBaseFileName>
      <BrowseInformation>true</BrowseInformation>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <Lib>
      <OutputFile>Debug\log4cppD.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;LOG4CPP_HAVE_BOOST;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Debug/log4cppLIB.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\Debug/</AssemblerListingLocation>
      <ObjectFileName>.\Debug/</ObjectFileName>
      <ProgramDataBaseFileName>.\Debug/</ProgramDataBaseFileName>
      <BrowseInformation>true</BrowseInformation>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <Lib>
      <OutputFile>Debug\log4cppD.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;LOG4CPP_HAVE_BOOST;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Release/log4cppLIB.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\Release/</AssemblerListingLocation>
      <ObjectFileName>.\Release/</ObjectFileName>
      <ProgramDataBaseFileName>.\Release/</ProgramDataBaseFileName>
      <BrowseInformation>true</BrowseInformation>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <Lib>
      <OutputFile>.\Release\log4cppLIB.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;LOG4CPP_HAVE_BOOST;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Release/log4cppLIB.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\Release/</AssemblerListingLocation>
      <ObjectFileName>.\Release/</ObjectFileName>
      <ProgramDataBaseFileName>.\Release/</ProgramDataBaseFileName>
      <BrowseInformation>true</BrowseInformation>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <Lib>
      <OutputFile>.\Release\log4cppLIB.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\src\AbortAppender.cpp" />
    <ClCompile Include="..\..\src\Appender.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\AppendersFactory.cpp" />
    <ClCompile Include="..\..\src\AppenderSkeleton.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\BasicConfigurator.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\BasicLayout.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\BufferingAppender.cpp" />
    <ClCompile Include="..\..\src\Category.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\CategoryStream.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\Configurator.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\DummyThreads.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\FactoryParams.cpp" />
    <ClCompile Include="..\..\src\FileAppender.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\Filter.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\FixedContextCategory.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\HierarchyMaintainer.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\IdsaAppender.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\LayoutAppender.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\LayoutsFactory.cpp" />
    <ClCompile Include="..\..\src\LevelEvaluator.cpp" />
    <ClCompile Include="..\..\src\Localtime.cpp" />
    <ClCompile Include="..\..\src\LoggingEvent.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\MSThreads.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\NDC.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\NTEventLogAppender.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\OmniThreads.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\OstreamAppender.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\PassThroughLayout.cpp" />
    <ClCompile Include="..\..\src\PatternLayout.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\PortabilityImpl.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\Priority.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\Properties.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\PropertyConfigurator.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\PropertyConfiguratorImpl.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\PThreads.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\RemoteSyslogAppender.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\RollingFileAppender.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\DailyRollingFileAppender.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\SimpleConfigurator.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\SimpleLayout.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\SmtpAppender.cpp" />
    <ClCompile Include="..\..\src\StringQueueAppender.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\StringUtil.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\TimeStamp.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
    <ClCompile Include="..\..\src\TriggeringEventEvaluatorFactory.cpp" />
    <ClCompile Include="..\..\src\Win32DebugAppender.cpp">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Disabled</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EnableFastChecks</BasicRuntimeChecks>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">true</BrowseInformation>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</BrowseInformation>
      <BrowseInformation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BrowseInformation>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\include\log4cpp\Appender.hh" />
    <None Include="..\..\include\log4cpp\AppendersFactory.hh" />
    <None Include="..\..\include\log4cpp\AppenderSkeleton.hh" />
    <None Include="..\..\include\log4cpp\BasicConfigurator.hh" />
    <None Include="..\..\include\log4cpp\BasicLayout.hh" />
    <None Include="..\..\include\log4cpp\threading\BoostThreads.hh" />
    <None Include="..\..\include\log4cpp\BufferingAppender.hh" />
    <None Include="..\..\include\log4cpp\Category.hh" />
    <None Include="..\..\include\log4cpp\CategoryStream.hh" />
    <None Include="..\..\include\log4cpp\Configurator.hh" />
    <None Include="..\..\include\log4cpp\ConfiguratorSkeleton.hh" />
    <None Include="..\..\include\log4cpp\threading\DummyThreads.hh" />
    <None Include="..\..\include\log4cpp\Evaluator.hh" />
    <None Include="..\..\include\log4cpp\Export.hh" />
    <None Include="..\..\include\log4cpp\FactoryParams.hh" />
    <None Include="..\..\include\log4cpp\FileAppender.hh" />
    <None Include="..\..\include\log4cpp\Filter.hh" />
    <None Include="..\..\include\log4cpp\FixedContextCategory.hh" />
    <None Include="..\..\include\log4cpp\HierarchyMaintainer.hh" />
    <None Include="..\..\include\log4cpp\IdsaAppender.hh" />
    <None Include="..\..\include\log4cpp\Layout.hh" />
    <None Include="..\..\include\log4cpp\LayoutAppender.hh" />
    <None Include="..\..\include\log4cpp\LayoutsFactory.hh" />
    <None Include="..\..\include\log4cpp\LevelEvaluator.hh" />
    <None Include="..\..\include\log4cpp\LoggingEvent.hh" />
    <None Include="..\..\include\log4cpp\threading\MSThreads.hh" />
    <None Include="..\..\include\log4cpp\NDC.hh" />
    <None Include="..\..\include\log4cpp\NTEventLogAppender.hh" />
    <None Include="..\..\include\log4cpp\threading\OmniThreads.hh" />
    <None Include="..\..\include\log4cpp\OstreamAppender.hh" />
    <None Include="..\..\include\log4cpp\PassThroughLayout.hh" />
    <None Include="..\..\include\log4cpp\PatternLayout.hh" />
    <None Include="..\..\include\log4cpp\Portability.hh" />
    <None Include="..\..\src\Localtime.hh" />
    <None Include="..\src\PortabilityImpl.hh" />
    <None Include="..\..\include\log4cpp\Priority.hh" />
    <None Include="..\..\src\Properties.hh" />
    <None Include="..\..\include\log4cpp\PropertyConfigurator.hh" />
    <None Include="..\..\include\log4cpp\RemoteSyslogAppender.hh" />
    <None Include="..\..\include\log4cpp\DailyRollingFileAppender.hh" />
    <None Include="..\..\include\log4cpp\RollingFileAppender.hh" />
    <None Include="..\..\include\log4cpp\SimpleConfigurator.hh" />
    <None Include="..\..\include\log4cpp\SimpleLayout.hh" />
    <None Include="..\..\include\log4cpp\SmtpAppender.hh" />
    <None Include="..\..\include\log4cpp\StringQueueAppender.hh" />
    <None Include="..\..\include\log4cpp\SyslogAppender.hh" />
    <None Include="..\..\include\log4cpp\threading\Threading.hh" />
    <None Include="..\..\include\log4cpp\TimeStamp.hh" />
    <None Include="..\..\include\log4cpp\TriggeringEventEvaluator.hh" />
    <None Include="..\..\include\log4cpp\TriggeringEventEvaluatorFactory.hh" />
    <None Include="..\..\include\log4cpp\Win32DebugAppender.hh" />
    <CustomBuild Include="..\NTEventLogCategories.mc">
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">if not exist $(OutDir) md $(OutDir)
mc.exe -h $(OutDir) -r $(OutDir) $(ProjectDir)..\%(Filename).mc
RC.exe -r -fo $(OutDir)%(Filename).res $(OutDir)%(Filename).rc
link.exe /MACHINE:IX86 -dll -noentry -out:$(OutDir)NTEventLogAppender.dll $(OutDir)%(Filename).res
</Command>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">if not exist $(OutDir) md $(OutDir)
mc.exe -h $(OutDir) -r $(OutDir) $(ProjectDir)..\%(Filename).mc
RC.exe -r -fo $(OutDir)%(Filename).res $(OutDir)%(Filename).rc
link.exe /MACHINE:IX86 -dll -noentry -out:$(OutDir)NTEventLogAppender.dll $(OutDir)%(Filename).res
</Command>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|Win32'">$(OutDir)NTEventLogAppender.dll;%(Outputs)</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug with Boost|x64'">$(OutDir)NTEventLogAppender.dll;%(Outputs)</Outputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">if not exist $(OutDir) md $(OutDir)
mc.exe -h $(OutDir) -r $(OutDir) $(ProjectDir)..\%(Filename).mc
RC.exe -r -fo $(OutDir)%(Filename).res $(OutDir)%(Filename).rc
link.exe /MACHINE:IX86 -dll -noentry -out:$(OutDir)NTEventLogAppender.dll $(OutDir)%(Filename).res
</Command>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">if not exist $(OutDir) md $(OutDir)
mc.exe -h $(OutDir) -r $(OutDir) $(ProjectDir)..\%(Filename).mc
RC.exe -r -fo $(OutDir)%(Filename).res $(OutDir)%(Filename).rc
link.exe /MACHINE:IX86 -dll -noentry -out:$(OutDir)NTEventLogAppender.dll $(OutDir)%(Filename).res
</Command>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(OutDir)NTEventLogAppender.dll;%(Outputs)</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(OutDir)NTEventLogAppender.dll;%(Outputs)</Outputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">if not exist $(OutDir) md $(OutDir)
mc.exe -h $(OutDir) -r $(OutDir) $(ProjectDir)..\%(Filename).mc
RC.exe -r -fo $(OutDir)%(Filename).res $(OutDir)%(Filename).rc
link.exe /MACHINE:IX86 -dll -noentry -out:$(OutDir)NTEventLogAppender.dll $(OutDir)%(Filename).res
</Command>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">if not exist $(OutDir) md $(OutDir)
mc.exe -h $(OutDir) -r $(OutDir) $(ProjectDir)..\%(Filename).mc
RC.exe -r -fo $(OutDir)%(Filename).res $(OutDir)%(Filename).rc
link.exe /MACHINE:IX86 -dll -noentry -out:$(OutDir)NTEventLogAppender.dll $(OutDir)%(Filename).res
</Command>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release with Boost|Win32'">$(OutDir)NTEventLogAppender.dll;%(Outputs)</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release with Boost|x64'">$(OutDir)NTEventLogAppender.dll;%(Outputs)</Outputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">if not exist $(OutDir) md $(OutDir)
mc.exe -h $(OutDir) -r $(OutDir) $(ProjectDir)..\%(Filename).mc
RC.exe -r -fo $(OutDir)%(Filename).res $(OutDir)%(Filename).rc
link.exe /MACHINE:IX86 -dll -noentry -out:$(OutDir)NTEventLogAppender.dll $(OutDir)%(Filename).res
</Command>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">if not exist $(OutDir) md $(OutDir)
mc.exe -h $(OutDir) -r $(OutDir) $(ProjectDir)..\%(Filename).mc
RC.exe -r -fo $(OutDir)%(Filename).res $(OutDir)%(Filename).rc
link.exe /MACHINE:IX86 -dll -noentry -out:$(OutDir)NTEventLogAppender.dll $(OutDir)%(Filename).res
</Command>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(OutDir)NTEventLogAppender.dll;%(Outputs)</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(OutDir)NTEventLogAppender.dll;%(Outputs)</Outputs>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\include\log4cpp\config-win32.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
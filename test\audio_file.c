#include "audio_processor.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>

/**
 * 打开音频文件进行处理
 * @param handler 文件处理器
 * @param input_path 输入PCM文件路径
 * @param output_path 输出PCM文件路径
 * @param ref_path 参考信号文件路径（可为NULL）
 * @return 0成功，-1失败
 */
int audio_file_open(AudioFileHandler *handler, const char *input_path, 
                   const char *output_path, const char *ref_path) {
    if (!handler || !input_path || !output_path) {
        fprintf(stderr, "Error: Invalid parameters for audio_file_open\n");
        return -1;
    }
    
    // 初始化结构体
    memset(handler, 0, sizeof(AudioFileHandler));
    
    // 打开输入文件
    handler->input_file = fopen(input_path, "rb");
    if (!handler->input_file) {
        fprintf(stderr, "Error: Cannot open input file %s: %s\n", 
                input_path, strerror(errno));
        return -1;
    }
    
    // 打开输出文件
    handler->output_file = fopen(output_path, "wb");
    if (!handler->output_file) {
        fprintf(stderr, "Error: Cannot open output file %s: %s\n", 
                output_path, strerror(errno));
        fclose(handler->input_file);
        return -1;
    }
    
    // 打开参考信号文件（可选）
    if (ref_path) {
        handler->ref_file = fopen(ref_path, "rb");
        if (!handler->ref_file) {
            fprintf(stderr, "Warning: Cannot open reference file %s: %s\n", 
                    ref_path, strerror(errno));
            handler->has_reference = false;
        } else {
            handler->has_reference = true;
            printf("Reference file opened: %s\n", ref_path);
        }
    } else {
        handler->has_reference = false;
    }
    
    // 计算输入文件总样本数
    fseek(handler->input_file, 0, SEEK_END);
    long file_size = ftell(handler->input_file);
    fseek(handler->input_file, 0, SEEK_SET);
    
    // 4声道，每样本2字节
    handler->total_samples = file_size / (CHANNELS * BYTES_PER_SAMPLE);
    handler->processed_samples = 0;
    
    printf("Input file opened: %s\n", input_path);
    printf("Output file opened: %s\n", output_path);
    printf("Total samples per channel: %lu\n", handler->total_samples);
    printf("Estimated duration: %.2f seconds\n", 
           (double)handler->total_samples / SAMPLE_RATE);
    
    return 0;
}

/**
 * 关闭所有音频文件
 * @param handler 文件处理器
 */
void audio_file_close(AudioFileHandler *handler) {
    if (!handler) return;
    
    if (handler->input_file) {
        fclose(handler->input_file);
        handler->input_file = NULL;
    }
    
    if (handler->output_file) {
        fclose(handler->output_file);
        handler->output_file = NULL;
    }
    
    if (handler->ref_file) {
        fclose(handler->ref_file);
        handler->ref_file = NULL;
    }
    
    printf("Audio files closed. Processed %lu/%lu samples\n", 
           handler->processed_samples, handler->total_samples);
}

/**
 * 读取一帧4声道交错音频数据
 * @param handler 文件处理器
 * @param buffer 输出缓冲区
 * @param samples 每声道样本数
 * @return 实际读取的样本数，0表示文件结束，-1表示错误
 */
int audio_file_read_frame(AudioFileHandler *handler, int16_t *buffer, int samples) {
    if (!handler || !handler->input_file || !buffer) {
        return -1;
    }
    
    // 读取交错的4声道数据
    size_t total_samples = samples * CHANNELS;
    size_t bytes_to_read = total_samples * BYTES_PER_SAMPLE;
    
    size_t bytes_read = fread(buffer, 1, bytes_to_read, handler->input_file);
    
    if (bytes_read == 0) {
        if (feof(handler->input_file)) {
            return 0;  // 文件结束
        } else {
            fprintf(stderr, "Error reading from input file: %s\n", strerror(errno));
            return -1;
        }
    }
    
    // 计算实际读取的样本数
    int samples_read = bytes_read / (CHANNELS * BYTES_PER_SAMPLE);
    
    // 如果读取不完整，用零填充
    if (samples_read < samples) {
        size_t remaining_bytes = (samples - samples_read) * CHANNELS * BYTES_PER_SAMPLE;
        memset((char*)buffer + bytes_read, 0, remaining_bytes);
    }
    
    handler->processed_samples += samples_read;
    return samples_read;
}

/**
 * 读取参考信号数据
 * @param handler 文件处理器
 * @param buffer 输出缓冲区
 * @param samples 样本数
 * @return 实际读取的样本数
 */
int audio_file_read_reference(AudioFileHandler *handler, int16_t *buffer, int samples) {
    if (!handler || !handler->ref_file || !buffer) {
        // 如果没有参考信号，填充零
        memset(buffer, 0, samples * BYTES_PER_SAMPLE);
        return samples;
    }
    
    size_t bytes_to_read = samples * BYTES_PER_SAMPLE;
    size_t bytes_read = fread(buffer, 1, bytes_to_read, handler->ref_file);
    
    int samples_read = bytes_read / BYTES_PER_SAMPLE;
    
    // 如果读取不完整，用零填充
    if (samples_read < samples) {
        memset((char*)buffer + bytes_read, 0, 
               (samples - samples_read) * BYTES_PER_SAMPLE);
    }
    
    return samples;
}

/**
 * 写入处理后的音频帧
 * @param handler 文件处理器
 * @param buffer 音频数据
 * @param samples 样本数
 * @return 0成功，-1失败
 */
int audio_file_write_frame(AudioFileHandler *handler, const int16_t *buffer, int samples) {
    if (!handler || !handler->output_file || !buffer) {
        return -1;
    }
    
    size_t bytes_to_write = samples * BYTES_PER_SAMPLE;
    size_t bytes_written = fwrite(buffer, 1, bytes_to_write, handler->output_file);
    
    if (bytes_written != bytes_to_write) {
        fprintf(stderr, "Error writing to output file: %s\n", strerror(errno));
        return -1;
    }
    
    return 0;
}

/**
 * 获取处理进度百分比
 * @param handler 文件处理器
 * @return 进度百分比 (0-100)
 */
double audio_file_get_progress(const AudioFileHandler *handler) {
    if (!handler || handler->total_samples == 0) {
        return 0.0;
    }
    
    return (double)handler->processed_samples / handler->total_samples * 100.0;
}

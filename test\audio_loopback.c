#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <alsa/asoundlib.h>
#include <signal.h>
#include <unistd.h>
#include <pthread.h>

// 音频参数
#define SAMPLE_RATE     16000
#define CHANNELS_IN     2
#define CHANNELS_OUT    2
#define FRAME_SIZE      512      // 减小帧大小以减少延迟
#define BUFFER_SIZE     16384    // 进一步增加缓冲区大小

// 全局变量
static volatile int g_running = 1;
static snd_pcm_t *capture_handle = NULL;
static snd_pcm_t *playback_handle = NULL;

// 音频处理参数
#define VOLUME_GAIN 5.0     // 增益倍数
#define NOISE_GATE 500      // 噪声门限值

// 信号处理函数
void signal_handler(int sig) {
    printf("\nReceived signal %d, stopping...\n", sig);
    g_running = 0;
}

// 打印ALSA错误
void print_error(const char *func, int err) {
    fprintf(stderr, "ALSA error in %s: %s\n", func, snd_strerror(err));
}

// 打印设备能力
void print_device_info(snd_pcm_t *handle, const char *name) {
    snd_pcm_hw_params_t *params;
    snd_pcm_hw_params_alloca(&params);
    
    printf("=== Device Info: %s ===\n", name);
    
    if (snd_pcm_hw_params_any(handle, params) < 0) {
        printf("Cannot get hardware parameters\n");
        return;
    }
    
    // 获取声道数范围
    unsigned int min_channels, max_channels;
    snd_pcm_hw_params_get_channels_min(params, &min_channels);
    snd_pcm_hw_params_get_channels_max(params, &max_channels);
    printf("Channels: %u-%u\n", min_channels, max_channels);
    
    // 获取采样率范围
    unsigned int min_rate, max_rate;
    int dir;
    snd_pcm_hw_params_get_rate_min(params, &min_rate, &dir);
    snd_pcm_hw_params_get_rate_max(params, &max_rate, &dir);
    printf("Sample rates: %u-%u Hz\n", min_rate, max_rate);
    
    // 获取格式
    printf("Formats: ");
    snd_pcm_format_t format;
    for (format = 0; format <= SND_PCM_FORMAT_LAST; format++) {
        if (snd_pcm_hw_params_test_format(handle, params, format) == 0) {
            printf("%s ", snd_pcm_format_name(format));
        }
    }
    printf("\n");
    
    printf("======================\n");
}

// 配置ALSA设备 - 返回实际的声道数，采样率通过全局变量传递
int configure_device(snd_pcm_t *handle, int channels, int is_capture) {
    snd_pcm_hw_params_t *params;
    int err;
    
    snd_pcm_hw_params_alloca(&params);
    
    // 初始化参数
    if ((err = snd_pcm_hw_params_any(handle, params)) < 0) {
        print_error("snd_pcm_hw_params_any", err);
        return -1;
    }
    
    // 设置访问模式
    if ((err = snd_pcm_hw_params_set_access(handle, params, 
                                           SND_PCM_ACCESS_RW_INTERLEAVED)) < 0) {
        print_error("snd_pcm_hw_params_set_access", err);
        return -1;
    }
    
    // 设置格式 - 尝试多种格式
    snd_pcm_format_t formats[] = {
        SND_PCM_FORMAT_S16_LE,
        SND_PCM_FORMAT_S32_LE,
        SND_PCM_FORMAT_FLOAT_LE,
        SND_PCM_FORMAT_S24_LE
    };

    int format_set = 0;
    for (int i = 0; i < 4; i++) {
        if ((err = snd_pcm_hw_params_set_format(handle, params, formats[i])) >= 0) {
            printf("%s using format: %s\n",
                   is_capture ? "Capture" : "Playback",
                   snd_pcm_format_name(formats[i]));
            format_set = 1;
            break;
        }
    }

    if (!format_set) {
        print_error("snd_pcm_hw_params_set_format (all formats failed)", err);
        return -1;
    }
    
    // 设置采样率 - 对于录音设备，使用其支持的采样率
    static unsigned int capture_rate = 0;
    unsigned int rate;

    if (is_capture) {
        // 对于录音设备，尝试获取支持的采样率
        unsigned int min_rate, max_rate;
        snd_pcm_hw_params_get_rate_min(params, &min_rate, 0);
        snd_pcm_hw_params_get_rate_max(params, &max_rate, 0);

        // 使用设备支持的采样率
        rate = max_rate;
        capture_rate = rate;  // 保存录音设备的采样率
        printf("Using capture device native rate: %u Hz\n", rate);
    } else {
        // 对于播放设备，使用与录音设备相同的采样率
        rate = capture_rate > 0 ? capture_rate : SAMPLE_RATE;
        printf("Setting playback rate to match capture: %u Hz\n", rate);
    }

    if ((err = snd_pcm_hw_params_set_rate_near(handle, params, &rate, 0)) < 0) {
        print_error("snd_pcm_hw_params_set_rate_near", err);
        return -1;
    }

    printf("%s using sample rate: %u Hz\n",
           is_capture ? "Capture" : "Playback", rate);
    
    // 设置声道数
    if ((err = snd_pcm_hw_params_set_channels(handle, params, channels)) < 0) {
        print_error("snd_pcm_hw_params_set_channels", err);
        
        // 尝试其他声道数
        if (is_capture) {
            // 对于录音设备，尝试2声道或1声道
            if ((err = snd_pcm_hw_params_set_channels(handle, params, 2)) < 0) {
                if ((err = snd_pcm_hw_params_set_channels(handle, params, 1)) < 0) {
                    print_error("snd_pcm_hw_params_set_channels (all attempts)", err);
                    return -1;
                } else {
                    channels = 1;
                }
            } else {
                channels = 2;
            }
        } else {
            // 对于播放设备，尝试1声道
            if ((err = snd_pcm_hw_params_set_channels(handle, params, 1)) < 0) {
                print_error("snd_pcm_hw_params_set_channels (all attempts)", err);
                return -1;
            } else {
                channels = 1;
            }
        }
    }
    
    printf("%s using %d channels\n", is_capture ? "Capture" : "Playback", channels);
    
    // 设置周期大小
    snd_pcm_uframes_t period_size = FRAME_SIZE;
    if ((err = snd_pcm_hw_params_set_period_size_near(handle, params, 
                                                     &period_size, 0)) < 0) {
        print_error("snd_pcm_hw_params_set_period_size_near", err);
        return -1;
    }
    
    // 设置缓冲区大小
    snd_pcm_uframes_t buffer_size = BUFFER_SIZE;
    if ((err = snd_pcm_hw_params_set_buffer_size_near(handle, params, 
                                                     &buffer_size)) < 0) {
        print_error("snd_pcm_hw_params_set_buffer_size_near", err);
        return -1;
    }
    
    // 应用参数
    if ((err = snd_pcm_hw_params(handle, params)) < 0) {
        print_error("snd_pcm_hw_params", err);
        return -1;
    }
    
    printf("%s configured: rate=%d, period=%lu, buffer=%lu\n", 
           is_capture ? "Capture" : "Playback", rate, period_size, buffer_size);
    
    return channels;
}

// 查找USB麦克风设备
int find_usb_mic(char *device_name, size_t size) {
    snd_ctl_t *handle;
    snd_ctl_card_info_t *info;
    snd_pcm_info_t *pcminfo;
    int card = -1;
    
    snd_ctl_card_info_alloca(&info);
    snd_pcm_info_alloca(&pcminfo);
    
    while (snd_card_next(&card) >= 0 && card >= 0) {
        char name[32];
        snprintf(name, sizeof(name), "hw:%d", card);
        
        if (snd_ctl_open(&handle, name, 0) < 0) continue;
        if (snd_ctl_card_info(handle, info) < 0) {
            snd_ctl_close(handle);
            continue;
        }
        
        const char *card_name = snd_ctl_card_info_get_name(info);
        printf("Found card %d: %s\n", card, card_name);
        
        if (strstr(card_name, "USB") || strstr(card_name, "Audio") || card == 2) {
            int dev = -1;
            while (snd_ctl_pcm_next_device(handle, &dev) >= 0 && dev >= 0) {
                snd_pcm_info_set_device(pcminfo, dev);
                snd_pcm_info_set_subdevice(pcminfo, 0);
                snd_pcm_info_set_stream(pcminfo, SND_PCM_STREAM_CAPTURE);
                
                if (snd_ctl_pcm_info(handle, pcminfo) >= 0) {
                    snprintf(device_name, size, "hw:%d,%d", card, dev);
                    printf("Found USB capture device: %s (%s)\n", device_name, card_name);
                    snd_ctl_close(handle);
                    return 0;
                }
            }
            
            // 如果没找到特定设备，使用卡本身
            snprintf(device_name, size, "hw:%d", card);
            printf("Using USB card device: %s (%s)\n", device_name, card_name);
            snd_ctl_close(handle);
            return 0;
        }
        
        snd_ctl_close(handle);
    }
    
    // 如果没找到，使用默认设备
    snprintf(device_name, size, "default");
    printf("No USB device found, using default\n");
    return 0;
}

// 主函数
int main(int argc, char *argv[]) {
    int err;
    char capture_device[256] = "default";
    char playback_device[256] = "default";
    int capture_channels = CHANNELS_IN;
    int playback_channels = CHANNELS_OUT;
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    printf("=== Simple Audio Loopback Test ===\n");
    
    // 查找USB麦克风
    find_usb_mic(capture_device, sizeof(capture_device));
    
    // 打开录音设备
    if ((err = snd_pcm_open(&capture_handle, capture_device, 
                           SND_PCM_STREAM_CAPTURE, 0)) < 0) {
        print_error("snd_pcm_open capture", err);
        return 1;
    }
    
    // 打开播放设备
    if ((err = snd_pcm_open(&playback_handle, playback_device, 
                           SND_PCM_STREAM_PLAYBACK, 0)) < 0) {
        print_error("snd_pcm_open playback", err);
        snd_pcm_close(capture_handle);
        return 1;
    }
    
    // 打印设备信息
    print_device_info(capture_handle, capture_device);
    print_device_info(playback_handle, playback_device);
    
    // 配置设备 - 先配置录音设备以获取其采样率
    int actual_capture_channels = configure_device(capture_handle, capture_channels, 1);
    if (actual_capture_channels < 0) {
        printf("Failed to configure capture device\n");
        goto cleanup;
    }

    // 然后配置播放设备，使其匹配录音设备的采样率
    int actual_playback_channels = configure_device(playback_handle, playback_channels, 0);
    
    if (actual_capture_channels < 0 || actual_playback_channels < 0) {
        printf("Failed to configure devices\n");
        goto cleanup;
    }
    
    // 准备设备
    if ((err = snd_pcm_prepare(capture_handle)) < 0) {
        print_error("snd_pcm_prepare capture", err);
        goto cleanup;
    }
    
    if ((err = snd_pcm_prepare(playback_handle)) < 0) {
        print_error("snd_pcm_prepare playback", err);
        goto cleanup;
    }
    
    // 分配缓冲区 - 使用更大的缓冲区以支持32位格式
    void *buffer = malloc(FRAME_SIZE * actual_capture_channels * 4); // 4字节/样本
    void *out_buffer = malloc(FRAME_SIZE * actual_playback_channels * 4);

    // 用于转换的临时缓冲区
    int16_t *temp_buffer = malloc(FRAME_SIZE * actual_playback_channels * sizeof(int16_t));
    
    if (!buffer || !out_buffer || !temp_buffer) {
        printf("Failed to allocate buffers\n");
        goto cleanup;
    }
    
    printf("Starting audio loopback...\n");
    printf("Press Ctrl+C to stop\n");
    
    // 启动录音
    if ((err = snd_pcm_start(capture_handle)) < 0) {
        print_error("snd_pcm_start", err);
        goto cleanup;
    }
    
    // 主循环
    int frames = 0;
    while (g_running) {
        // 读取数据
        err = snd_pcm_readi(capture_handle, buffer, FRAME_SIZE);
        
        if (err == -EPIPE) {
            printf("Capture underrun\n");
            snd_pcm_prepare(capture_handle);
            continue;
        } else if (err < 0) {
            print_error("snd_pcm_readi", err);
            if (snd_pcm_recover(capture_handle, err, 0) < 0) {
                break;
            }
            continue;
        }
        
        // 转换32位输入到16位输出，并进行简单的降采样
        int32_t *input_32 = (int32_t*)buffer;

        // 简单的降采样：96kHz -> 48kHz (每2个样本取1个)
        int downsample_ratio = 2;  // 96kHz/48kHz = 2
        int output_samples = FRAME_SIZE / downsample_ratio;

        for (int i = 0; i < output_samples; i++) {
            // 从32位转换到16位（右移16位），并进行降采样
            int32_t sample_32 = input_32[(i * downsample_ratio) * actual_capture_channels];
            int16_t raw_sample = (int16_t)(sample_32 >> 16);

            // 应用噪声门控
            int16_t sample = raw_sample;
            if (abs(raw_sample) < NOISE_GATE) {
                sample = 0;  // 静音小信号
            } else {
                // 应用音量增益
                float sample_float = (float)raw_sample * VOLUME_GAIN;

                // 限制在16位范围内
                if (sample_float > 32767.0f) sample_float = 32767.0f;
                if (sample_float < -32768.0f) sample_float = -32768.0f;

                sample = (int16_t)sample_float;
            }

            // 如果是多声道输出，复制到所有声道
            for (int ch = 0; ch < actual_playback_channels; ch++) {
                temp_buffer[i * actual_playback_channels + ch] = sample;
            }
        }

        // 更新实际要写入的样本数
        int samples_to_write = output_samples;
        
        // 写入数据 - 使用转换后的缓冲区和正确的样本数
        err = snd_pcm_writei(playback_handle, temp_buffer, samples_to_write);
        
        if (err == -EPIPE) {
            printf("Playback underrun\n");
            snd_pcm_prepare(playback_handle);
            continue;
        } else if (err < 0) {
            print_error("snd_pcm_writei", err);
            if (snd_pcm_recover(playback_handle, err, 0) < 0) {
                break;
            }
            continue;
        }
        
        frames++;
        if (frames % 100 == 0) {
            // 计算音频电平
            int32_t *debug_input = (int32_t*)buffer;
            int32_t max_input = 0;
            for (int i = 0; i < FRAME_SIZE; i++) {
                int32_t val = abs(debug_input[i * actual_capture_channels]);
                if (val > max_input) max_input = val;
            }

            int16_t max_output = 0;
            for (int i = 0; i < samples_to_write * actual_playback_channels; i++) {
                int16_t val = abs(temp_buffer[i]);
                if (val > max_output) max_output = val;
            }

            printf("Frame %d: Input max=%d, Output max=%d\n", frames, max_input >> 16, max_output);
        }
    }
    
cleanup:
    // 清理
    if (buffer) free(buffer);
    if (out_buffer) free(out_buffer);
    if (temp_buffer) free(temp_buffer);
    
    if (capture_handle) {
        snd_pcm_drop(capture_handle);
        snd_pcm_close(capture_handle);
    }
    
    if (playback_handle) {
        snd_pcm_drop(playback_handle);
        snd_pcm_close(playback_handle);
    }
    
    printf("Audio loopback test finished\n");
    return 0;
}

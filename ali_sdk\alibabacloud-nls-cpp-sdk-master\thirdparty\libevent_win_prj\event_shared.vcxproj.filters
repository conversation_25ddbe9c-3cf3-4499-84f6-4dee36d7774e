﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="..\buffer.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\buffer_iocp.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\bufferevent.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\bufferevent_async.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\bufferevent_filter.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\bufferevent_pair.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\bufferevent_ratelim.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\bufferevent_sock.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\event.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\evmap.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\evutil.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\evutil_rand.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\evutil_time.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\log.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\event_iocp.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\evthread.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\evthread_win32.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\listener.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\signal.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\strlcpy.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\win32select.c">
      <Filter>Source Core</Filter>
    </ClCompile>
    <ClCompile Include="..\evdns.c">
      <Filter>Source Extra</Filter>
    </ClCompile>
    <ClCompile Include="..\event_tagging.c">
      <Filter>Source Extra</Filter>
    </ClCompile>
    <ClCompile Include="..\evrpc.c">
      <Filter>Source Extra</Filter>
    </ClCompile>
    <ClCompile Include="..\http.c">
      <Filter>Source Extra</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Core">
      <UniqueIdentifier>{453F2D9D-E89B-364B-ABEA-76D4AB056A9D}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Extra">
      <UniqueIdentifier>{F3397A6C-A067-3297-89FC-6E48301764F6}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
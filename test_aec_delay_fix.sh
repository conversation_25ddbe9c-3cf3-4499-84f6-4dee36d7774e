#!/bin/bash

# AEC延时修复测试脚本
# 用于验证延时补偿修复效果

echo "🔧 AEC延时修复测试脚本"
echo "========================"

# 服务器地址
SERVER="http://localhost:8080"

# 检查服务器状态
echo "📊 检查当前状态..."
curl -s "$SERVER/status" | jq '.'

echo ""
echo "🔄 测试延时调整功能..."

# 测试不同的延时值
DELAYS=(30 45 60 75 94)

for delay in "${DELAYS[@]}"; do
    echo "⏱️  设置延时为 ${delay}ms..."
    response=$(curl -s -X POST "$SERVER/set_aec_delay" \
        -H "Content-Type: application/json" \
        -d "{\"delay_ms\": $delay}")
    
    echo "响应: $response"
    
    # 等待一下让系统适应
    sleep 2
    
    # 检查状态
    status=$(curl -s "$SERVER/status")
    current_delay=$(echo "$status" | jq -r '.aec_reference_delay_ms')
    echo "✅ 当前延时: ${current_delay}ms"
    echo ""
done

echo "🤖 测试自动调整功能..."

# 启用自动调整
echo "🔧 启用自动调整..."
curl -s -X POST "$SERVER/set_auto_adjust" \
    -H "Content-Type: application/json" \
    -d '{"enabled": true}' | jq '.'

echo ""
echo "📊 检查最终状态..."
curl -s "$SERVER/status" | jq '.'

echo ""
echo "✅ 测试完成！"
echo ""
echo "📝 修复说明："
echo "1. 修正了延时样本数计算错误（不再乘以声道数）"
echo "2. 添加了动态延时验证和自适应调整"
echo "3. 提供了HTTP API控制自动调整功能"
echo "4. 改进了延时补偿的精确性"
echo ""
echo "🎯 预期效果："
echo "- 延时补偿更精确（94ms对应4512个样本而不是9024个）"
echo "- 系统会自动检测和调整最优延时"
echo "- 回声消除效果应该显著改善"

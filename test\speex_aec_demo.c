#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <string.h>
#include <speex/speex_echo.h>

// WAV 文件头结构体
typedef struct {
    char riff[4];           // "RIFF"
    uint32_t chunk_size;    // 文件大小 - 8
    char wave[4];           // "WAVE"
    char fmt[4];            // "fmt "
    uint32_t fmt_size;      // 16
    uint16_t audio_format;  // PCM = 1
    uint16_t num_channels;  // 通道数
    uint32_t sample_rate;   // 采样率
    uint32_t byte_rate;     // 字节率
    uint16_t block_align;   // 块对齐
    uint16_t bits_per_sample; // 位深度
    char data[4];           // "data"
    uint32_t data_size;     // 数据大小
} WavHeader;

// 参数定义
#define SAMPLE_RATE 48000
#define FRAME_SIZE 960    // 20ms @ 48kHz
#define CHANNELS 2
#define TRUE_DELAY_SAMPLES 4800 // 100ms 延迟
#define MIN_RMS_THRESHOLD 10.0f
#define BASE_CORR_THRESHOLD 0.1f
#define MAX_DELAY_MS 400
#define MIN_DELAY_MS 20

// 计算 RMS
float calc_rms(int16_t *buffer, int length) {
    if (!buffer) return 0.0f;
    double sum = 0.0;
    for (int i = 0; i < length; i++) {
        sum += (double)buffer[i] * buffer[i];
    }
    return sqrt(sum / length);
}

// 延迟估计函数
int estimate_delay(int16_t *mic_buffer, int16_t *ref_buffer, int length, int sample_rate, int channels) {
    if (!mic_buffer || !ref_buffer) {
        printf("错误: 缓冲区为空\n");
        return -1;
    }

    int16_t *mic_mono = (int16_t *)calloc(length, sizeof(int16_t));
    int16_t *ref_mono = (int16_t *)calloc(length, sizeof(int16_t));
    if (!mic_mono || !ref_mono) {
        printf("错误: 内存分配失败 (mic_mono=%p, ref_mono=%p)\n", mic_mono, ref_mono);
        free(mic_mono);
        free(ref_mono);
        return -1;
    }

    for (int i = 0; i < length; i++) {
        if (channels == 2) {
            mic_mono[i] = (mic_buffer[i * 2] + mic_buffer[i * 2 + 1]) / 2;
            ref_mono[i] = (ref_buffer[i * 2] + ref_buffer[i * 2 + 1]) / 2;
        } else {
            mic_mono[i] = mic_buffer[i];
            ref_mono[i] = ref_buffer[i];
        }
    }

    float mic_rms = calc_rms(mic_mono, length);
    float ref_rms = calc_rms(ref_mono, length);
    printf("信号 RMS: mic=%.2f, ref=%.2f\n", mic_rms, ref_rms);
    if (mic_rms < MIN_RMS_THRESHOLD || ref_rms < MIN_RMS_THRESHOLD) {
        printf("信号太弱: mic_rms=%.2f, ref_rms=%.2f\n", mic_rms, ref_rms);
        free(mic_mono);
        free(ref_mono);
        return -1;
    }

    float corr_threshold = BASE_CORR_THRESHOLD;
    if (mic_rms < 30.0f || ref_rms < 30.0f) {
        corr_threshold = 0.05f;
    }

    float gain = (mic_rms < 80.0f || ref_rms < 80.0f) ? 5.0f : 1.0f;
    for (int i = 0; i < length; i++) {
        mic_mono[i] *= gain;
        ref_mono[i] *= gain;
    }

    float *mic_norm = (float *)calloc(length, sizeof(float));
    float *ref_norm = (float *)calloc(length, sizeof(float));
    if (!mic_norm || !ref_norm) {
        printf("错误: 归一化缓冲区分配失败 (mic_norm=%p, ref_norm=%p)\n", mic_norm, ref_norm);
        free(mic_mono);
        free(ref_mono);
        free(mic_norm);
        free(ref_norm);
        return -1;
    }

    mic_rms = calc_rms(mic_mono, length);
    ref_rms = calc_rms(ref_mono, length);
    for (int i = 0; i < length; i++) {
        mic_norm[i] = mic_mono[i] / (mic_rms + 1e-6f);
        ref_norm[i] = ref_mono[i] / (ref_rms + 1e-6f);
    }

    int max_delay = sample_rate / 1000 * MAX_DELAY_MS;
    int min_delay = sample_rate / 1000 * MIN_DELAY_MS;
    int step = 4;
    int best_delay = -1;
    float max_corr = -1.0f;

    for (int d = min_delay; d < max_delay; d += step) {
        float corr = 0.0f;
        int count = 0;
        for (int i = 0; i < length - d; i += step) {
            corr += mic_norm[i + d] * ref_norm[i];
            count++;
        }
        corr /= (count > 0) ? count : 1;
        if (corr > max_corr && corr > corr_threshold) {
            max_corr = corr;
            best_delay = d;
        }
    }

    free(mic_mono);
    free(ref_mono);
    free(mic_norm);
    free(ref_norm);

    if (best_delay == -1) {
        printf("无有效相关性峰值 (max_corr=%.2f)\n", max_corr);
        return -1;
    }

    printf("延迟估计: %d samples (%.2f ms, corr=%.2f)\n",
           best_delay, best_delay * 1000.0f / sample_rate, max_corr);
    return best_delay;
}

// 保存 WAV 文件
void save_wav(const char *filename, int16_t *buffer, int samples, int channels, int sample_rate) {
    if (!buffer) {
        printf("错误: 无法保存 WAV 文件，缓冲区为空 (%s)\n", filename);
        return;
    }

    WavHeader header = {
            .riff = {'R', 'I', 'F', 'F'},
            .chunk_size = 36 + samples * channels * 2,
            .wave = {'W', 'A', 'V', 'E'},
            .fmt = {'f', 'm', 't', ' '},
            .fmt_size = 16,
            .audio_format = 1,
            .num_channels = channels,
            .sample_rate = sample_rate,
            .byte_rate = sample_rate * channels * 2,
            .block_align = channels * 2,
            .bits_per_sample = 16,
            .data = {'d', 'a', 't', 'a'},
            .data_size = samples * channels * 2
    };

    FILE *f = fopen(filename, "wb");
    if (!f) {
        printf("错误: 无法打开文件 %s\n", filename);
        return;
    }
    fwrite(&header, sizeof(WavHeader), 1, f);
    fwrite(buffer, sizeof(int16_t), samples * channels, f);
    fclose(f);
    printf("保存 WAV 文件: %s\n", filename);
}

int main() {
    // 初始化 SpeexDSP
    int sample_rate = SAMPLE_RATE;
    SpeexEchoState *echo_state = speex_echo_state_init(FRAME_SIZE, FRAME_SIZE * 10); // 尾长 200ms
    if (!echo_state) {
        printf("错误: SpeexDSP 初始化失败\n");
        return 1;
    }
    speex_echo_ctl(echo_state, SPEEX_ECHO_SET_SAMPLING_RATE, &sample_rate);

    // 分配缓冲区
    int buffer_size = FRAME_SIZE * CHANNELS;
    int16_t *ref_buffer = (int16_t *)calloc(buffer_size, sizeof(int16_t));
    int16_t *mic_buffer = (int16_t *)calloc(buffer_size, sizeof(int16_t));
    int16_t *aec_buffer = (int16_t *)calloc(FRAME_SIZE, sizeof(int16_t));
    int16_t *ref_mono = (int16_t *)calloc(FRAME_SIZE, sizeof(int16_t));
    if (!ref_buffer || !mic_buffer || !aec_buffer || !ref_mono) {
        printf("错误: 缓冲区分配失败 (ref=%p, mic=%p, aec=%p, ref_mono=%p)\n",
               ref_buffer, mic_buffer, aec_buffer, ref_mono);
        free(ref_buffer);
        free(mic_buffer);
        free(aec_buffer);
        free(ref_mono);
        speex_echo_state_destroy(echo_state);
        return 1;
    }

    // 生成模拟扬声器信号（弱信号，RMS ≈ 70）
    for (int i = 0; i < FRAME_SIZE; i++) {
        int16_t sample = (int16_t)(100 * sin(2 * M_PI * 440 * i / SAMPLE_RATE));
        ref_buffer[i * 2] = sample;
        ref_buffer[i * 2 + 1] = sample;
    }

    // 生成模拟麦克风信号（含回声、目标语音、噪声）
    for (int i = 0; i < FRAME_SIZE; i++) {
        if (i + TRUE_DELAY_SAMPLES / CHANNELS < FRAME_SIZE) {
            // 回声（衰减 0.5，延迟 100ms）
            mic_buffer[(i + TRUE_DELAY_SAMPLES / CHANNELS) * 2] = ref_buffer[i * 2] * 0.5;
            mic_buffer[(i + TRUE_DELAY_SAMPLES / CHANNELS) * 2 + 1] = ref_buffer[i * 2 + 1] * 0.5;
        }
        // 添加目标语音（880Hz）
        int16_t voice = (int16_t)(200 * sin(2 * M_PI * 880 * i / SAMPLE_RATE));
        mic_buffer[i * 2] += voice;
        mic_buffer[i * 2 + 1] += voice;
        // 添加噪声
        mic_buffer[i * 2] += (rand() % 100 - 50); // 降低噪声幅度
        mic_buffer[i * 2 + 1] += (rand() % 100 - 50);
    }

    // 保存输入信号
    save_wav("ref.wav", ref_buffer, FRAME_SIZE, CHANNELS, SAMPLE_RATE);
    save_wav("mic.wav", mic_buffer, FRAME_SIZE, CHANNELS, SAMPLE_RATE);

    // 延迟估计
    int delay_samples = estimate_delay(mic_buffer, ref_buffer, FRAME_SIZE, SAMPLE_RATE, CHANNELS);
    printf("延迟估计结果: %d samples (%.2f ms)\n", delay_samples, delay_samples * 1000.0f / SAMPLE_RATE);

    // 转换为单声道参考信号，并应用延迟
    for (int i = 0; i < FRAME_SIZE; i++) {
        int ref_idx = (delay_samples >= 0 && i >= delay_samples) ? i - delay_samples : i;
        if (ref_idx * 2 + 1 < buffer_size) {
            ref_mono[i] = (ref_buffer[ref_idx * 2] + ref_buffer[ref_idx * 2 + 1]) / 2;
        } else {
            ref_mono[i] = 0; // 避免越界
        }
    }

    // 执行回声消除
    speex_echo_cancellation(echo_state, mic_buffer, ref_mono, aec_buffer);

    // 保存输出信号
    save_wav("aec.wav", aec_buffer, FRAME_SIZE, 1, SAMPLE_RATE);

    // 清理
    speex_echo_state_destroy(echo_state);
    free(ref_buffer);
    free(mic_buffer);
    free(aec_buffer);
    free(ref_mono);

    printf("测试完成，生成 WAV 文件: ref.wav, mic.wav, aec.wav\n");
    return 0;
}
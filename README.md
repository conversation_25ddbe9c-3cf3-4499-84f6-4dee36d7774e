# 4声道麦克风阵列音频处理器

这是一个专为Realtek USB 4声道麦克风阵列（设备ID: 0bda:4015）设计的音频信号处理项目，实现了波束形成、回声消除、噪声抑制和自动增益控制功能。

## 功能特性

- **波束形成 (Beamforming)**: 使用Delay-and-Sum算法增强目标方向声音
- **回声消除 (AEC)**: 基于SpeexDSP库去除扬声器回声
- **噪声抑制 (NS)**: 减少环境噪声干扰
- **自动增益控制 (AGC)**: 自动调整输出音量
- **语音活动检测 (VAD)**: 检测语音活动状态

## 系统要求

- **操作系统**: Ubuntu 20.04+ (ARM64/x86_64)
- **硬件**: Realtek USB 4声道麦克风阵列
- **依赖库**: SpeexDSP, CMake 3.22+, GCC

## 安装依赖

### Ubuntu/Debian系统

```bash
# 安装基本构建工具
sudo apt update
sudo apt install build-essential cmake pkg-config

# 安装SpeexDSP库
sudo apt install libspeexdsp-dev

# 验证安装
pkg-config --modversion speexdsp
```

### 从源码编译SpeexDSP (可选)

```bash
# 下载SpeexDSP源码
wget http://downloads.xiph.org/releases/speex/speexdsp-1.2.1.tar.gz
tar -xzf speexdsp-1.2.1.tar.gz
cd speexdsp-1.2.1

# 编译安装
./configure --prefix=/usr/local
make -j$(nproc)
sudo make install
sudo ldconfig
```

## 编译项目

```bash
# 克隆或进入项目目录
cd mic_dev

# 创建构建目录
mkdir build
cd build

# 配置和编译
cmake ..
make -j$(nproc)

# 验证编译结果
./mic_dev --help
```

## 使用方法

### 基本用法

```bash
# 处理4声道PCM文件
./mic_dev input_4ch.pcm output_1ch.pcm

# 带参考信号的回声消除
./mic_dev input_4ch.pcm output_1ch.pcm reference.pcm
```

### 处理测试文件

```bash
# 处理您的测试文件
./mic_dev /home/<USER>/test20s.pcm processed_output.pcm
```

### 输入文件格式

- **采样率**: 16kHz
- **位深**: 16-bit
- **声道**: 4声道交错格式
- **格式**: 原始PCM数据
- **声道顺序**: [mic1, mic2, mic3, mic4, mic1, mic2, mic3, mic4, ...]

### 输出文件格式

- **采样率**: 16kHz
- **位深**: 16-bit
- **声道**: 单声道
- **格式**: 原始PCM数据

## 项目结构

```
mic_dev/
├── src/
│   ├── audio_processor.h      # 主要头文件定义
│   ├── main.c                 # 主程序入口
│   ├── audio_file.c           # PCM文件读写模块
│   ├── channel_processing.c   # 声道分离和处理
│   ├── beamforming.c         # 波束形成算法
│   └── speex_processor.c     # SpeexDSP集成
├── doc/
│   └── 功能说明.txt          # 详细功能说明
├── CMakeLists.txt            # 构建配置
└── README.md                 # 本文件
```

## 算法参数

### 波束形成参数

- **麦克风间距**: 5cm (可在audio_processor.h中调整)
- **目标角度**: 0° (正前方)
- **最大延迟**: 8个样本
- **阵列类型**: 线性阵列

### SpeexDSP参数

- **帧大小**: 160样本 (10ms @ 16kHz)
- **滤波器长度**: 160样本
- **回声尾长度**: 1600样本 (100ms)
- **噪声抑制**: -25dB
- **AGC目标电平**: 20000

## 性能优化

### 实时处理

项目设计支持实时处理，典型性能指标：

- **处理延迟**: ~10ms (单帧处理)
- **CPU使用率**: <50% (ARM Cortex-A72)
- **内存使用**: <10MB

### 优化建议

1. **编译优化**: 使用`-O2`或`-O3`优化级别
2. **NEON优化**: 在ARM平台启用NEON指令集
3. **多线程**: 可考虑将波束形成和SpeexDSP处理并行化

## 调试和测试

### 启用调试信息

```bash
# 编译调试版本
cmake -DCMAKE_BUILD_TYPE=Debug ..
make

# 运行并查看详细输出
./mic_dev input.pcm output.pcm
```

### 性能分析

```bash
# 使用time命令测量性能
time ./mic_dev /home/<USER>/test20s.pcm output.pcm

# 使用valgrind检查内存泄漏
valgrind --leak-check=full ./mic_dev input.pcm output.pcm
```

## 常见问题

### 1. SpeexDSP库未找到

```bash
# 检查pkg-config配置
pkg-config --list-all | grep speex

# 手动指定库路径
export PKG_CONFIG_PATH=/usr/local/lib/pkgconfig:$PKG_CONFIG_PATH
```

### 2. 音频质量问题

- 检查麦克风阵列物理安装
- 调整波束形成目标角度
- 优化SpeexDSP参数设置

### 3. 性能问题

- 使用Release模式编译
- 检查系统CPU和内存使用情况
- 考虑减少帧大小或简化算法

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。

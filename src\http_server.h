#ifndef HTTP_SERVER_H
#define HTTP_SERVER_H

#include <microhttpd.h>
#include <stdint.h>

// HTTP服务器配置
#define HTTP_SERVER_PORT 8080
#define MAX_RESPONSE_SIZE 1024

// HTTP服务器状态
typedef struct {
    struct MHD_Daemon *daemon;
    int running;
    int port;
} HttpServer;

// 全局变量声明（在audio_realtime.c中定义）
extern volatile int g_aec_reference_delay_ms;

// 函数声明
int http_server_init(HttpServer *server, int port);
void http_server_cleanup(HttpServer *server);
int http_server_start(HttpServer *server);
void http_server_stop(HttpServer *server);

// HTTP请求处理函数
static int request_handler(void *cls, struct MHD_Connection *connection,
                          const char *url, const char *method,
                          const char *version, const char *upload_data,
                          size_t *upload_data_size, void **con_cls);

// 各个接口的处理函数
static int handle_get_status(struct MHD_Connection *connection);
static int handle_post_set_aec_delay(struct MHD_Connection *connection,
                                     const char *upload_data,
                                     size_t upload_data_size);
static int handle_options_request(struct MHD_Connection *connection);
static int handle_not_found(struct MHD_Connection *connection);

#endif // HTTP_SERVER_H

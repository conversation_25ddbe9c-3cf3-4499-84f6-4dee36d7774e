#ifndef LOGGER_H
#define LOGGER_H

#include <stdio.h>
#include <stdarg.h>
#include <stdbool.h>

/**
 * 日志级别定义
 */
typedef enum {
    LOG_LEVEL_TRACE = 0,    // 最详细的调试信息
    LOG_LEVEL_DEBUG = 1,    // 调试信息
    LOG_LEVEL_INFO = 2,     // 一般信息
    LOG_LEVEL_WARN = 3,     // 警告信息
    LOG_LEVEL_ERROR = 4,    // 错误信息
    LOG_LEVEL_FATAL = 5,    // 致命错误
    LOG_LEVEL_OFF = 6       // 关闭日志
} LogLevel;

/**
 * 模块ID定义（每个源文件对应一个模块）
 */
typedef enum {
    MODULE_MAIN = 0,
    MODULE_AUDIO_REALTIME,
    MODULE_VOICE_DIALOG,
    MODULE_VOICE_EVENTS,
    MODULE_SPEECH_UPLOAD,
    MODULE_ALI_ASR,
    MODULE_APP_RESAMPLER,
    MODULE_AUDIO_PROCESSOR,
    MODULE_BEAMFORMING,
    MODULE_SPEEX_PROCESSOR,
    MODULE_CHANNEL_PROCESSING,
    MODULE_COUNT  // 模块总数
} ModuleId;

/**
 * 初始化日志系统
 * @param default_level 默认日志级别
 * @return 0成功，-1失败
 */
int logger_init(LogLevel default_level);

/**
 * 清理日志系统
 */
void logger_cleanup(void);

/**
 * 设置指定模块的日志级别
 * @param module_id 模块ID
 * @param level 日志级别
 */
void logger_set_module_level(ModuleId module_id, LogLevel level);

/**
 * 获取指定模块的日志级别
 * @param module_id 模块ID
 * @return 日志级别
 */
LogLevel logger_get_module_level(ModuleId module_id);

/**
 * 设置全局日志级别
 * @param level 日志级别
 */
void logger_set_global_level(LogLevel level);

/**
 * 检查指定模块是否应该输出指定级别的日志
 * @param module_id 模块ID
 * @param level 日志级别
 * @return true应该输出，false不应该输出
 */
bool logger_should_log(ModuleId module_id, LogLevel level);

/**
 * 输出日志
 * @param module_id 模块ID
 * @param level 日志级别
 * @param file 文件名
 * @param line 行号
 * @param func 函数名
 * @param format 格式字符串
 * @param ... 参数
 */
void logger_log(ModuleId module_id, LogLevel level, const char *file, int line, 
                const char *func, const char *format, ...);

/**
 * 获取日志级别名称
 * @param level 日志级别
 * @return 级别名称
 */
const char* logger_level_name(LogLevel level);

/**
 * 获取模块名称
 * @param module_id 模块ID
 * @return 模块名称
 */
const char* logger_module_name(ModuleId module_id);

// 便捷宏定义
#define LOG_TRACE(module, ...) logger_log(module, LOG_LEVEL_TRACE, __FILE__, __LINE__, __func__, __VA_ARGS__)
#define LOG_DEBUG(module, ...) logger_log(module, LOG_LEVEL_DEBUG, __FILE__, __LINE__, __func__, __VA_ARGS__)
#define LOG_INFO(module, ...)  logger_log(module, LOG_LEVEL_INFO,  __FILE__, __LINE__, __func__, __VA_ARGS__)
#define LOG_WARN(module, ...)  logger_log(module, LOG_LEVEL_WARN,  __FILE__, __LINE__, __func__, __VA_ARGS__)
#define LOG_ERROR(module, ...) logger_log(module, LOG_LEVEL_ERROR, __FILE__, __LINE__, __func__, __VA_ARGS__)
#define LOG_FATAL(module, ...) logger_log(module, LOG_LEVEL_FATAL, __FILE__, __LINE__, __func__, __VA_ARGS__)

// 简化宏（不包含文件名和行号，适合替换printf）
#define LOGI(module, ...) do { if (logger_should_log(module, LOG_LEVEL_INFO)) { printf(__VA_ARGS__); printf("\n"); } } while(0)
#define LOGD(module, ...) do { if (logger_should_log(module, LOG_LEVEL_DEBUG)) { printf(__VA_ARGS__); printf("\n"); } } while(0)
#define LOGW(module, ...) do { if (logger_should_log(module, LOG_LEVEL_WARN)) { printf(__VA_ARGS__); printf("\n"); } } while(0)
#define LOGE(module, ...) do { if (logger_should_log(module, LOG_LEVEL_ERROR)) { printf(__VA_ARGS__); printf("\n"); } } while(0)

#endif // LOGGER_H

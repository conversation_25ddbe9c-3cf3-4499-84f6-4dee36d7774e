#include "logger.h"
#include <string.h>
#include <time.h>
#include <pthread.h>

// 全局日志配置
static LogLevel g_module_levels[MODULE_COUNT];
static LogLevel g_global_level = LOG_LEVEL_INFO;
static bool g_logger_initialized = false;
static pthread_mutex_t g_log_mutex = PTHREAD_MUTEX_INITIALIZER;

// 日志级别名称
static const char* g_level_names[] = {
    "TRACE", "DEBUG", "INFO ", "WARN ", "ERROR", "FATAL", "OFF  "
};

// 模块名称
static const char* g_module_names[] = {
    "MAIN",
    "AUDIO_RT",
    "VOICE_DLG",
    "VOICE_EVT",
    "SPEECH_UP",
    "ALI_ASR",
    "RESAMPLER",
    "AUDIO_PROC",
    "BEAMFORM",
    "SPEEX_PROC",
    "CHANNEL"
};

/**
 * 初始化日志系统
 */
int logger_init(LogLevel default_level) {
    pthread_mutex_lock(&g_log_mutex);
    
    g_global_level = default_level;
    
    // 初始化所有模块的日志级别
    for (int i = 0; i < MODULE_COUNT; i++) {
        g_module_levels[i] = default_level;
    }
    
    g_logger_initialized = true;
    
    pthread_mutex_unlock(&g_log_mutex);
    
    printf("🔧 Logger initialized with default level: %s\n", logger_level_name(default_level));
    return 0;
}

/**
 * 清理日志系统
 */
void logger_cleanup(void) {
    pthread_mutex_lock(&g_log_mutex);
    g_logger_initialized = false;
    pthread_mutex_unlock(&g_log_mutex);
    printf("🔧 Logger cleaned up\n");
}

/**
 * 设置指定模块的日志级别
 */
void logger_set_module_level(ModuleId module_id, LogLevel level) {
    if (module_id >= MODULE_COUNT) return;
    
    pthread_mutex_lock(&g_log_mutex);
    g_module_levels[module_id] = level;
    pthread_mutex_unlock(&g_log_mutex);
    
    printf("🔧 Module %s log level set to %s\n", 
           logger_module_name(module_id), logger_level_name(level));
}

/**
 * 获取指定模块的日志级别
 */
LogLevel logger_get_module_level(ModuleId module_id) {
    if (module_id >= MODULE_COUNT) return LOG_LEVEL_OFF;
    
    pthread_mutex_lock(&g_log_mutex);
    LogLevel level = g_module_levels[module_id];
    pthread_mutex_unlock(&g_log_mutex);
    
    return level;
}

/**
 * 设置全局日志级别
 */
void logger_set_global_level(LogLevel level) {
    pthread_mutex_lock(&g_log_mutex);
    g_global_level = level;
    
    // 更新所有模块的日志级别
    for (int i = 0; i < MODULE_COUNT; i++) {
        g_module_levels[i] = level;
    }
    pthread_mutex_unlock(&g_log_mutex);
    
    printf("🔧 Global log level set to %s\n", logger_level_name(level));
}

/**
 * 检查指定模块是否应该输出指定级别的日志
 */
bool logger_should_log(ModuleId module_id, LogLevel level) {
    if (!g_logger_initialized || module_id >= MODULE_COUNT) {
        return level >= LOG_LEVEL_ERROR; // 未初始化时只输出错误
    }
    
    pthread_mutex_lock(&g_log_mutex);
    bool should_log = (level >= g_module_levels[module_id]);
    pthread_mutex_unlock(&g_log_mutex);
    
    return should_log;
}

/**
 * 输出日志
 */
void logger_log(ModuleId module_id, LogLevel level, const char *file, int line, 
                const char *func, const char *format, ...) {
    if (!logger_should_log(module_id, level)) {
        return;
    }
    
    // 获取当前时间
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);
    
    // 获取文件名（去掉路径）
    const char *filename = strrchr(file, '/');
    if (filename) {
        filename++;
    } else {
        filename = file;
    }
    
    pthread_mutex_lock(&g_log_mutex);
    
    // 输出简洁的日志格式：时间戳、级别、模块
    printf("[%02d:%02d:%02d] [%s] [%s] ",
           tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec,
           logger_level_name(level),
           logger_module_name(module_id));
    
    // 输出实际日志内容
    va_list args;
    va_start(args, format);
    vprintf(format, args);
    va_end(args);
    
    printf("\n");
    fflush(stdout);
    
    pthread_mutex_unlock(&g_log_mutex);
}

/**
 * 获取日志级别名称
 */
const char* logger_level_name(LogLevel level) {
    if (level >= LOG_LEVEL_TRACE && level <= LOG_LEVEL_OFF) {
        return g_level_names[level];
    }
    return "UNKNOWN";
}

/**
 * 获取模块名称
 */
const char* logger_module_name(ModuleId module_id) {
    if (module_id >= 0 && module_id < MODULE_COUNT) {
        return g_module_names[module_id];
    }
    return "UNKNOWN";
}

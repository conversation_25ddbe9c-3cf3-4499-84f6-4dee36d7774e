
#ifndef MIC_DEV_AUDIO_COVERT_H
#define MIC_DEV_AUDIO_COVERT_H

#include "app_resampler.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <sys/stat.h>
#include <unistd.h>
#include <ctype.h>
#include "logger.h"

// Opus编码参数
#define OPUS_FRAME_SIZE 960        // 每帧样本数（20ms @ 48kHz）
#define OPUS_MAX_PACKET_SIZE 4000  // 最大Opus数据包大小
#define OPUS_BITRATE 64000         // 比特率（64kbps，适合语音）


int convert_pcm_to_opus_file(const int16_t *pcm_data, size_t pcm_size_bytes,
                             int sample_rate, int channels, const char *opus_filename);

int convert_pcm_to_opus_bytes(const int16_t *pcm_data, size_t pcm_size_bytes,
                              int sample_rate, int channels,
                              unsigned char **opus_data_ptr, size_t *opus_size);

#endif //MIC_DEV_AUDIO_COVERT_H

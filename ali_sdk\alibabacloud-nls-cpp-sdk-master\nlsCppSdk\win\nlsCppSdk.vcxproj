<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{4755b663-bf8d-4b83-bc49-0ddbbdecf802}</ProjectGuid>
    <RootNamespace>nlsCppSdk</RootNamespace>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <CLRSupport>false</CLRSupport>
    <SupportLTL>false</SupportLTL>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <CLRSupport>false</CLRSupport>
    <SupportLTL>false</SupportLTL>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <CLRSupport>false</CLRSupport>
    <SupportLTL>false</SupportLTL>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <CLRSupport>false</CLRSupport>
    <DisableAdvancedSupport>false</DisableAdvancedSupport>
    <SupportLTL>false</SupportLTL>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)..\..\build\build_win32\nlsCppSdk\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)..\..\build\build_win32\nlsCppSdk\$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)..\..\build\build_win32\nlsCppSdk\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)..\..\build\build_win32\nlsCppSdk\$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)..\..\build\build_win64\nlsCppSdk\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)..\..\build\build_win64\nlsCppSdk\$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)..\..\build\build_win64\nlsCppSdk\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)..\..\build\build_win64\nlsCppSdk\$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>false</SDLCheck>
      <PreprocessorDefinitions>_NLS_SDK_SHARED_;WIN32_LEAN_AND_MEAN;_CRT_SECURE_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;HAVE_STRUCT_TIMESPEC;_DEBUG;_CONSOLE;WIN32;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>$(SolutionDir)..\..\build\build_win32\thirdparty\curl-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\libevent-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\log4cpp-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\jsoncpp-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\openssl-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\opus-prefix\include;$(SolutionDir)..\framework\feature;$(SolutionDir)..\framework\feature\da;$(SolutionDir)..\framework\feature\sy;$(SolutionDir)..\framework\feature\st;$(SolutionDir)..\framework\feature\sr;$(SolutionDir)..\framework\item;$(SolutionDir)..\framework\common;$(SolutionDir)..\token\include\internal;$(SolutionDir)..\token\include;$(SolutionDir)..\utils;$(SolutionDir)..\transport;$(SolutionDir)..\event;$(SolutionDir)..\encoder</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(SolutionDir)libs_14.0\x86\Debug</AdditionalLibraryDirectories>
      <AdditionalDependencies>lib_json.lib;libevent.lib;libcurld.lib;log4cppD.lib;opus.lib;libcrypto-1_1.lib;libssl-1_1.lib;Rpcrt4.lib;Crypt32.lib;Iphlpapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>false</SDLCheck>
      <PreprocessorDefinitions>_NLS_SDK_SHARED_;WIN32_LEAN_AND_MEAN;_CRT_SECURE_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;HAVE_STRUCT_TIMESPEC;WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>$(SolutionDir)..\..\build\build_win32\thirdparty\curl-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\libevent-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\log4cpp-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\jsoncpp-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\openssl-prefix\include;$(SolutionDir)..\..\build\build_win32\thirdparty\opus-prefix\include;$(SolutionDir)..\framework\feature;$(SolutionDir)..\framework\feature\da;$(SolutionDir)..\framework\feature\sy;$(SolutionDir)..\framework\feature\st;$(SolutionDir)..\framework\feature\sr;$(SolutionDir)..\framework\item;$(SolutionDir)..\framework\common;$(SolutionDir)..\token\include\internal;$(SolutionDir)..\token\include;$(SolutionDir)..\utils;$(SolutionDir)..\transport;$(SolutionDir)..\event;$(SolutionDir)..\encoder</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(SolutionDir)libs_14.0\x86\Release</AdditionalLibraryDirectories>
      <AdditionalDependencies>lib_jsonMD.lib;libevent.lib;libcurl.lib;log4cppLIBMD.lib;opus.lib;libcrypto-1_1.lib;libssl-1_1.lib;Rpcrt4.lib;Crypt32.lib;Iphlpapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>false</SDLCheck>
      <PreprocessorDefinitions>_NLS_SDK_SHARED_;WIN32_LEAN_AND_MEAN;_CRT_SECURE_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;HAVE_STRUCT_TIMESPEC;_WIN64;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>$(SolutionDir)..\..\build\build_win64\thirdparty\curl-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\libevent-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\log4cpp-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\jsoncpp-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\openssl-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\opus-prefix\include;$(SolutionDir)..\framework\feature;$(SolutionDir)..\framework\feature\da;$(SolutionDir)..\framework\feature\sy;$(SolutionDir)..\framework\feature\st;$(SolutionDir)..\framework\feature\sr;$(SolutionDir)..\framework\item;$(SolutionDir)..\framework\common;$(SolutionDir)..\token\include\internal;$(SolutionDir)..\token\include;$(SolutionDir)..\utils;$(SolutionDir)..\transport;$(SolutionDir)..\event;$(SolutionDir)..\encoder</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>lib_json.lib;libcurld.lib;libevent.lib;log4cpp.lib;opus.lib;libcrypto-1_1-x64.lib;libssl-1_1-x64.lib;Rpcrt4.lib;Crypt32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(SolutionDir)libs_14.0\x64\Debug</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>false</SDLCheck>
      <PreprocessorDefinitions>_NLS_SDK_SHARED_;WIN32_LEAN_AND_MEAN;_CRT_SECURE_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;HAVE_STRUCT_TIMESPEC;_WIN64;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>$(SolutionDir)..\..\build\build_win64\thirdparty\curl-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\libevent-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\log4cpp-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\jsoncpp-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\openssl-prefix\include;$(SolutionDir)..\..\build\build_win64\thirdparty\opus-prefix\include;$(SolutionDir)..\framework\feature;$(SolutionDir)..\framework\feature\da;$(SolutionDir)..\framework\feature\sy;$(SolutionDir)..\framework\feature\st;$(SolutionDir)..\framework\feature\sr;$(SolutionDir)..\framework\item;$(SolutionDir)..\framework\common;$(SolutionDir)..\token\include\internal;$(SolutionDir)..\token\include;$(SolutionDir)..\utils;$(SolutionDir)..\transport;$(SolutionDir)..\event;$(SolutionDir)..\encoder</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(SolutionDir)libs_14.0\x64\Release;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>lib_jsonMD.lib;libcurl.lib;libeventMD.lib;log4cppMD.lib;opus.lib;libcrypto-1_1-x64.lib;libssl-1_1-x64.lib;Rpcrt4.lib;Crypt32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <ProjectReference>
      <UseLibraryDependencyInputs>false</UseLibraryDependencyInputs>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\encoder\nlsEncoder.cpp" />
    <ClCompile Include="..\event\workThread.cpp" />
    <ClCompile Include="..\framework\common\nlsClient.cpp" />
    <ClCompile Include="..\framework\common\nlsEvent.cpp" />
    <ClCompile Include="..\framework\feature\da\dialogAssistantListener.cpp" />
    <ClCompile Include="..\framework\feature\da\dialogAssistantParam.cpp" />
    <ClCompile Include="..\framework\feature\da\dialogAssistantRequest.cpp" />
    <ClCompile Include="..\framework\feature\sr\speechRecognizerListener.cpp" />
    <ClCompile Include="..\framework\feature\sr\speechRecognizerParam.cpp" />
    <ClCompile Include="..\framework\feature\sr\speechRecognizerRequest.cpp" />
    <ClCompile Include="..\framework\feature\st\speechTranscriberListener.cpp" />
    <ClCompile Include="..\framework\feature\st\speechTranscriberParam.cpp" />
    <ClCompile Include="..\framework\feature\st\speechTranscriberRequest.cpp" />
    <ClCompile Include="..\framework\feature\sy\speechSynthesizerListener.cpp" />
    <ClCompile Include="..\framework\feature\sy\speechSynthesizerParam.cpp" />
    <ClCompile Include="..\framework\feature\sy\speechSynthesizerRequest.cpp" />
    <ClCompile Include="..\framework\item\iNlsRequest.cpp" />
    <ClCompile Include="..\framework\item\iNlsRequestListener.cpp" />
    <ClCompile Include="..\framework\item\iNlsRequestParam.cpp" />
    <ClCompile Include="..\token\src\ClientConfiguration.cpp" />
    <ClCompile Include="..\token\src\CommonClient.cpp" />
    <ClCompile Include="..\token\src\CommonRequest.cpp" />
    <ClCompile Include="..\token\src\CommonResponse.cpp" />
    <ClCompile Include="..\token\src\CoreClient.cpp" />
    <ClCompile Include="..\token\src\Credentials.cpp" />
    <ClCompile Include="..\token\src\CredentialsProvider.cpp" />
    <ClCompile Include="..\token\src\CurlHttpClient.cpp" />
    <ClCompile Include="..\token\src\Error.cpp" />
    <ClCompile Include="..\token\src\FileTrans.cpp" />
    <ClCompile Include="..\token\src\HmacSha1Signer.cpp" />
    <ClCompile Include="..\token\src\HttpClient.cpp" />
    <ClCompile Include="..\token\src\HttpMessage.cpp" />
    <ClCompile Include="..\token\src\HttpRequest.cpp" />
    <ClCompile Include="..\token\src\HttpResponse.cpp" />
    <ClCompile Include="..\token\src\NetworkProxy.cpp" />
    <ClCompile Include="..\token\src\nlsToken.cpp" />
    <ClCompile Include="..\token\src\ServiceRequest.cpp" />
    <ClCompile Include="..\token\src\Signer.cpp" />
    <ClCompile Include="..\token\src\SimpleCredentialsProvider.cpp" />
    <ClCompile Include="..\token\src\Url.cpp" />
    <ClCompile Include="..\token\src\Utils.cpp" />
    <ClCompile Include="..\transport\connectNode.cpp" />
    <ClCompile Include="..\transport\nlsEventNetWork.cpp" />
    <ClCompile Include="..\transport\nodeManager.cpp" />
    <ClCompile Include="..\transport\SSLconnect.cpp" />
    <ClCompile Include="..\transport\webSocketTcp.cpp" />
    <ClCompile Include="..\utils\nlog.cpp" />
    <ClCompile Include="..\utils\text_utils.cpp" />
    <ClCompile Include="..\utils\utility.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets" />
</Project>
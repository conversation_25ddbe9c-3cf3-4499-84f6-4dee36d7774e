#ifndef ALI_ASR_H
#define ALI_ASR_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include <pthread.h>


// 阿里云ASR配置
#define ALI_TOKEN_URL "http://192.168.6.22:8137/openai/v1/genAliToken"
#define ALI_ASR_URL "wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1"
#define MAX_TOKEN_SIZE 256
#define MAX_APPKEY_SIZE 64
#define MAX_TASK_ID_SIZE 64
#define MAX_TEXT_SIZE 2048
#define MAX_ERROR_SIZE 512

// WebSocket帧大小配置
#define OPUS_FRAME_SIZE 640    // 20ms @ 16kHz
#define SEND_INTERVAL_MS 20    // 发送间隔20ms

// 阿里云Token响应结构
typedef struct {
    char token[MAX_TOKEN_SIZE];
    char appKey[MAX_APPKEY_SIZE];
    bool valid;
} AliTokenInfo;

// ASR识别结果结构
typedef struct {
    bool success;
    bool is_final;              // 是否为最终结果
    int sentence_id;            // 句子ID
    char text[MAX_TEXT_SIZE];   // 识别文本
    char task_id[MAX_TASK_ID_SIZE]; // 任务ID
    char error_msg[MAX_ERROR_SIZE]; // 错误信息
    double confidence;          // 置信度
    int begin_time;            // 开始时间(ms)
    int end_time;              // 结束时间(ms)
} AliASRResult;

// ASR回调函数类型
typedef void (*AliASRCallback)(const AliASRResult *result, void *user_data);

// ASR连接状态
typedef enum {
    ALI_ASR_DISCONNECTED = 0,
    ALI_ASR_CONNECTING,
    ALI_ASR_CONNECTED,
    ALI_ASR_STARTED,
    ALI_ASR_ERROR
} AliASRState;

// 全局ASR管理器
typedef struct {
    bool initialized;
    AliTokenInfo token_info;
    void *nls_client;           // NlsClient实例
    void *transcriber_request;  // SpeechTranscriberRequest实例
    AliASRState state;

    // 音频参数
    int sample_rate;
    int channels;

    // 回调
    AliASRCallback callback;
    void *user_data;

    // 线程同步
    pthread_mutex_t state_mutex;
    pthread_cond_t state_cond;
    pthread_mutex_t callback_mutex;

    // 控制标志
    bool running;
    bool stop_requested;
} AliASRManager;

// ASR实例结构
typedef struct {
    // 连接信息
    AliTokenInfo token_info;
    char task_id[MAX_TASK_ID_SIZE];
    AliASRState state;
    
    // WebSocket连接
    void *ws_conn;              // WebSocket连接句柄
    pthread_t ws_thread;        // WebSocket线程
    pthread_mutex_t state_mutex; // 状态锁
    pthread_cond_t state_cond;   // 状态条件变量
    
    // 音频参数
    int sample_rate;
    int channels;
    
    // 回调
    AliASRCallback callback;
    void *user_data;
    
    // 控制标志
    bool running;
    bool stop_requested;
} AliASRInstance;

// 函数声明
#ifdef __cplusplus
extern "C" {
#endif

/**
 * 初始化阿里云ASR模块（应用启动时调用一次）
 * @param sample_rate 采样率
 * @param channels 声道数
 * @param callback 结果回调函数
 * @param user_data 用户数据
 * @return 0成功，-1失败
 */
int ali_asr_init(int sample_rate, int channels, AliASRCallback callback, void *user_data);

/**
 * 清理阿里云ASR模块（应用退出时调用）
 */
void ali_asr_cleanup(void);

/**
 * 获取阿里云Token
 * @param token_info 输出Token信息
 * @return 0成功，-1失败
 */
int ali_asr_get_token(AliTokenInfo *token_info);

/**
 * 启动ASR连接（建立WebSocket连接）
 * @return 0成功，-1失败
 */
int ali_asr_start_connection(void);

/**
 * 停止ASR连接
 * @return 0成功，-1失败
 */
int ali_asr_stop_connection(void);

/**
 * 发送音频数据（实时调用）
 * @param audio_data 音频数据(PCM 16bit)
 * @param data_size 数据大小(字节)
 * @return 0成功，-1失败
 */
int ali_asr_send_audio(const unsigned char *audio_data, size_t data_size);

/**
 * 获取当前状态
 * @return 当前状态
 */
AliASRState ali_asr_get_state(void);

/**
 * 检查是否已连接并可以发送音频
 * @return true可以发送，false不可以
 */
bool ali_asr_is_ready(void);

/**
 * 打印ASR结果
 * @param result ASR结果
 */
void ali_asr_result_print(const AliASRResult *result);

#ifdef __cplusplus
}
#endif

#endif // ALI_ASR_H

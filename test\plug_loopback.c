#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <alsa/asoundlib.h>
#include <signal.h>
#include <unistd.h>

// 全局变量
static volatile int g_running = 1;

// 信号处理函数
void signal_handler(int sig) {
    printf("\nReceived signal %d, stopping...\n", sig);
    g_running = 0;
}

// 打印错误信息
void print_error(const char *func, int err) {
    fprintf(stderr, "ALSA error in %s: %s\n", func, snd_strerror(err));
}

// 主函数
int main(int argc, char *argv[]) {
    int err;
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    printf("=== ALSA Plug Device Loopback ===\n");
    
    // 使用plug设备，它会自动处理格式转换
    const char *capture_device = "plughw:2,0";  // USB麦克风
    const char *playback_device = "default";    // 默认播放设备
    
    printf("Using capture device: %s\n", capture_device);
    printf("Using playback device: %s\n", playback_device);
    
    // 打开设备
    snd_pcm_t *capture_handle, *playback_handle;
    
    if ((err = snd_pcm_open(&capture_handle, capture_device, SND_PCM_STREAM_CAPTURE, 0)) < 0) {
        print_error("snd_pcm_open capture", err);
        return 1;
    }
    
    if ((err = snd_pcm_open(&playback_handle, playback_device, SND_PCM_STREAM_PLAYBACK, 0)) < 0) {
        print_error("snd_pcm_open playback", err);
        snd_pcm_close(capture_handle);
        return 1;
    }
    
    // 使用相同的格式配置两个设备，让ALSA处理转换
    unsigned int rate = 48000;  // 使用常见的采样率
    unsigned int channels = 2;   // 立体声
    snd_pcm_format_t format = SND_PCM_FORMAT_S16_LE;  // 16位格式
    unsigned int latency = 100000;  // 100ms延迟，减少underrun
    
    // 配置录音设备
    if ((err = snd_pcm_set_params(capture_handle,
                                  format,
                                  SND_PCM_ACCESS_RW_INTERLEAVED,
                                  channels,
                                  rate,
                                  1,  // 允许重采样
                                  latency)) < 0) {
        print_error("snd_pcm_set_params capture", err);
        goto cleanup;
    }
    
    // 配置播放设备
    if ((err = snd_pcm_set_params(playback_handle,
                                  format,
                                  SND_PCM_ACCESS_RW_INTERLEAVED,
                                  channels,
                                  rate,
                                  1,  // 允许重采样
                                  latency)) < 0) {
        print_error("snd_pcm_set_params playback", err);
        goto cleanup;
    }
    
    printf("Both devices configured: %s, %uch, %uHz, latency=%uus\n", 
           snd_pcm_format_name(format), channels, rate, latency);
    
    // 计算帧大小
    snd_pcm_uframes_t frames_per_period = 1024;
    
    // 分配缓冲区
    int16_t *buffer = malloc(frames_per_period * channels * sizeof(int16_t));
    
    if (!buffer) {
        printf("Failed to allocate buffer\n");
        goto cleanup;
    }
    
    printf("Starting audio loopback with %lu frames per period...\n", frames_per_period);
    printf("Press Ctrl+C to stop\n");
    
    // 主循环
    int frame_count = 0;
    int consecutive_errors = 0;
    
    while (g_running && consecutive_errors < 10) {
        // 读取数据
        snd_pcm_sframes_t frames_read = snd_pcm_readi(capture_handle, buffer, frames_per_period);
        
        if (frames_read == -EPIPE) {
            printf("Capture underrun, recovering...\n");
            snd_pcm_prepare(capture_handle);
            consecutive_errors++;
            continue;
        } else if (frames_read < 0) {
            print_error("snd_pcm_readi", frames_read);
            if (snd_pcm_recover(capture_handle, frames_read, 0) < 0) {
                consecutive_errors++;
                if (consecutive_errors >= 10) {
                    printf("Too many consecutive errors, stopping\n");
                    break;
                }
                continue;
            }
            consecutive_errors++;
            continue;
        }
        
        // 重置错误计数器
        consecutive_errors = 0;
        
        // 应用简单的增益
        float gain = 5.0f;
        for (snd_pcm_uframes_t i = 0; i < frames_read * channels; i++) {
            float sample = buffer[i] * gain;
            if (sample > 32767.0f) sample = 32767.0f;
            if (sample < -32768.0f) sample = -32768.0f;
            buffer[i] = (int16_t)sample;
        }
        
        // 写入数据
        snd_pcm_sframes_t frames_written = snd_pcm_writei(playback_handle, buffer, frames_read);
        
        if (frames_written == -EPIPE) {
            printf("Playback underrun, recovering...\n");
            snd_pcm_prepare(playback_handle);
            continue;
        } else if (frames_written < 0) {
            print_error("snd_pcm_writei", frames_written);
            if (snd_pcm_recover(playback_handle, frames_written, 0) < 0) {
                break;
            }
            continue;
        }
        
        frame_count++;
        if (frame_count % 100 == 0) {
            // 计算音频电平
            int16_t max_level = 0;
            for (snd_pcm_uframes_t i = 0; i < frames_read * channels; i++) {
                int16_t val = abs(buffer[i]);
                if (val > max_level) max_level = val;
            }
            
            printf("Frame %d: Read=%ld, Written=%ld, Max_level=%d\n", 
                   frame_count, frames_read, frames_written, max_level);
        }
    }
    
cleanup:
    // 清理
    if (buffer) free(buffer);
    
    if (capture_handle) {
        snd_pcm_drop(capture_handle);
        snd_pcm_close(capture_handle);
    }
    
    if (playback_handle) {
        snd_pcm_drop(playback_handle);
        snd_pcm_close(playback_handle);
    }
    
    printf("Audio loopback finished\n");
    return 0;
}

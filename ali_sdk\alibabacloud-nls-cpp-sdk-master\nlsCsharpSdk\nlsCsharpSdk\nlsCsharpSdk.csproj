﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net472</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <LangVersion>preview</LangVersion>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <IsPublishable>False</IsPublishable>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <AutoGenerateBindingRedirects>False</AutoGenerateBindingRedirects>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <ProduceReferenceAssembly>True</ProduceReferenceAssembly>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    <Platforms>AnyCPU;x64;x86</Platforms>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <Optimize>True</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <Optimize>True</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x86'">
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <Optimize>True</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x86'">
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="x64\**" />
    <EmbeddedResource Remove="x64\**" />
    <None Remove="x64\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="App.config" />
    <None Remove="App1.config" />
    <None Remove="nlsCsharpSdkExtern.dll" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\nlsCsharpSdkExtern\nlsCsharpSdkExtern.vcxproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Net.Http" />
  </ItemGroup>

</Project>

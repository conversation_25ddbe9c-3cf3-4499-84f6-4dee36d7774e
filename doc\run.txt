# 语音对话系统运行指南

## 当前功能（完整API模式）
- 🎤 **唤醒检测**: 检测到声音能量超过阈值时自动唤醒
- 🗣️ **语音录制**: 自动录制用户语音，支持VAD检测
- 🔄 **高质量重采样**: 使用SpeexDSP进行48kHz→16kHz转换
- 🌐 **语音识别**: 自动上传到Whisper服务器进行语音识别
- 🔊 **TTS播报**: 自动将识别结果通过TTS播报
- 📁 **文件保存**: 保存原始PCM和转换后的WAV文件供调试

## 基本运行
```bash
# 使用默认配置运行
sudo /tmp/tmp.SdckuMV0nY/cmake-build-debug/mic_dev --realtime

# 指定设备运行
sudo /tmp/tmp.SdckuMV0nY/cmake-build-debug/mic_dev --realtime --capture-device plughw:2,0
```

## 服务器配置
### 语音识别服务器（Whisper兼容）
- **默认服务器**: http://************:8501/transcribe
- **录音格式**: 48kHz立体声 → 自动转换为16kHz单声道WAV
- **上传格式**: WAV文件（16-bit PCM, 16kHz, mono）
- **返回格式**: JSON（包含segments和text字段）
- **文本提取**: 自动从JSON中提取纯文本用于TTS
- **转换方式**: 设备端自动转换，减少服务器负载
- **上传方式**: multipart/form-data

### TTS播报服务器
- **默认服务器**: http://************:8230/speak
- **支持格式**: JSON文本数据
- **播报方式**: 立即播报模式

## 测试流程
1. **启动程序**: 程序开始监听音频（扬声器已禁用）
2. **唤醒检测**: 说话时自动检测到唤醒（能量阈值5000）
3. **开始录音**: 检测到真实人声时显示"🗣️ Real speech detected!"
4. **录音状态**: 每500帧显示录音进度和VAD状态
5. **结束录音**: 静音2秒后自动结束（只有真正的静音才计数）
6. **保存原始文件**: 保存48kHz立体声PCM文件到/tmp目录
7. **格式转换**: 自动转换为16kHz单声道WAV格式
8. **保存WAV文件**: 保存转换后的WAV文件到/tmp目录
9. **测试播放**: 可以使用aplay播放保存的WAV文件
10. **准备下次**: 自动重置状态，准备下次录音测试

## 输出示例
```
🎤 Wake word detected! Starting to listen...
🗣️ Real speech detected! Starting recording...
📊 Recording: 100 frames, Speech=15, Silence=0
📊 Recording: 200 frames, Speech=28, Silence=0
📊 Recording: 300 frames, Speech=25, Silence=5
🎯 Speech processing started, buffer size: 8000 samples
✅ Speech ended, processing 8000 samples
📁 Voice saved to: /tmp/voice_recording_1234567890.pcm
🌐 Uploading to server for speech recognition...
🎵 Original audio: 48000Hz, 2ch, 2.5s
🔄 Audio converted: 48000Hz 2ch → 16000Hz 1ch (240000 → 40000 samples)
📁 PCM converted to WAV: /tmp/speech_upload_1234567890.wav
📊 Format: 16000Hz, 1ch, 16-bit, 78.12 KB
✅ Audio converted to Whisper-compatible WAV format
✅ Upload successful (1.23s)
📝 Response: {"text": "你好，今天天气怎么样？"}
� Speech recognition successful, starting TTS playback...
🔊 Sending text to TTS server...
📝 Text: 你好，今天天气怎么样？
⚡ Mode: Immediate
✅ TTS request successful (0.45s)
🎉 Complete workflow: Speech → Recognition → TTS successful!
```

## 注意事项
- 需要sudo权限访问音频设备
- 确保语音识别服务器(************:8501)可访问
- 确保TTS播报服务器(************:8230)可访问
- 录音文件临时保存在/tmp目录
- 支持实时VAD语音活动检测
- TTS播报使用立即模式，会打断当前播报
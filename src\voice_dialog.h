#ifndef VOICE_DIALOG_H
#define VOICE_DIALOG_H

#include <stdint.h>
#include <stdbool.h>

#ifndef _WIN32
#include <pthread.h>
#else
#include <windows.h>
typedef CRITICAL_SECTION pthread_mutex_t;
#endif

// 语音对话状态
typedef enum {
    VOICE_STATE_IDLE,           // 空闲状态，等待唤醒
    VOICE_STATE_WAKE_DETECTED,  // 检测到唤醒词
    VOICE_STATE_LISTENING,      // 正在监听用户说话
    VOICE_STATE_PROCESSING,     // 处理语音数据
    VOICE_STATE_UPLOADING       // 上传到服务器
} VoiceDialogState;

// 语音数据缓冲区
typedef struct {
    int16_t *buffer;           // 音频数据缓冲区
    int capacity;              // 缓冲区容量（样本数）
    int size;                  // 当前数据大小（样本数）
    int sample_rate;           // 采样率
    int channels;              // 声道数
    pthread_mutex_t mutex;     // 线程安全
} VoiceBuffer;

// 新的回调函数类型定义
typedef void (*VoiceDialogReadyCallback)(void);                    // 1. 系统就绪
typedef void (*VoiceDialogStartCallback)(void);                    // 2. VAD检测到人声，开始录音
typedef void (*VoiceDialogProcessAudioCallback)(const uint8_t *opus_data, size_t opus_size,int sample_rate, int channels); // 3. 每100ms产生OPUS数据
typedef void (*VoiceDialogFinishCallback)(const uint8_t *opus_data, size_t opus_size,int sample_rate, int channels);       // 4. 完整的OPUS数据
typedef void (*VoiceDialogStopCallback)(void);                     // 5. 连续5秒无声音，停止录音
typedef void (*VoiceDialogCloseCallback)(void);                    // 6. 关闭连接

// 语音对话处理器
typedef struct {
    VoiceDialogState state;    // 当前状态
    VoiceBuffer *voice_buffer; // 语音数据缓冲区
    
    // VAD相关
    int speech_frames;         // 连续语音帧数
    int silence_frames;        // 连续静音帧数
    int vad_confirmation_count; // VAD确认帧计数
    int vad_threshold;         // VAD阈值
    
    // 录音控制
    bool is_recording;         // 是否正在录音
    int max_record_frames;     // 最大录音帧数
    int current_record_frames; // 当前录音帧数
    

    // 新的回调函数
    VoiceDialogReadyCallback on_ready;                 // 1. 系统就绪
    VoiceDialogStartCallback on_start;                 // 2. VAD检测到人声
    VoiceDialogProcessAudioCallback on_process_audio;  // 3. 每100ms产生OPUS数据
    VoiceDialogFinishCallback on_finish;               // 4. 完整的OPUS数据
    VoiceDialogStopCallback on_stop;                   // 5. 连续5秒无声音
    VoiceDialogCloseCallback on_close;                 // 6. 关闭连接

    pthread_mutex_t state_mutex;  // 状态锁
} VoiceDialogProcessor;

// 语音对话参数
#define VOICE_SAMPLE_RATE       16000    // 语音对话模块实际处理的采样率
#define VOICE_CHANNELS          1        // 语音对话模块实际处理的声道数
#define VOICE_FRAME_SIZE        160      // 10ms帧大小 @ 16kHz (16000 / 100)
//#define VOICE_MAX_DURATION      600       // 最大录音时长（秒）
#define VOICE_MAX_DURATION      60       // 最大录音时长（秒）
#define VOICE_SILENCE_THRESHOLD 500      // 静音帧阈值（约5秒@48kHz，10ms帧）
#define VOICE_SPEECH_THRESHOLD  10       // 语音帧阈值
#define VAD_CONFIRMATION_FRAMES 5        // VAD确认需要连续的帧数

// 唤醒词相关
#define WAKE_WORD_DEFAULT       "小助手"  // 默认唤醒词
#define WAKE_ENERGY_THRESHOLD   5000     // 唤醒能量阈值（提高以减少误触发）

// 函数声明
VoiceDialogProcessor* voice_dialog_create(void);
void voice_dialog_destroy(VoiceDialogProcessor *processor);

int voice_dialog_init(VoiceDialogProcessor *processor);
void voice_dialog_cleanup(VoiceDialogProcessor *processor);

// 状态控制
void voice_dialog_set_state(VoiceDialogProcessor *processor, VoiceDialogState state);
VoiceDialogState voice_dialog_get_state(VoiceDialogProcessor *processor);

// 音频处理
int voice_dialog_process_audio(VoiceDialogProcessor *processor, 
                              const int16_t *audio_data, int samples, int sample_rate, int channels ,
                              bool vad_result);

// 语音缓冲区操作
VoiceBuffer* voice_buffer_create(int capacity, int sample_rate, int channels);
void voice_buffer_destroy(VoiceBuffer *buffer);
int voice_buffer_append(VoiceBuffer *buffer, const int16_t *data, int samples);
int voice_buffer_clear(VoiceBuffer *buffer);
int voice_buffer_save_pcm(VoiceBuffer *buffer, const char *filename); 




// 新的回调函数设置
int voice_dialog_set_new_callbacks(VoiceDialogProcessor *processor,
                                   VoiceDialogReadyCallback on_ready,
                                   VoiceDialogStartCallback on_start,
                                   VoiceDialogProcessAudioCallback on_process_audio,
                                   VoiceDialogFinishCallback on_finish,
                                   VoiceDialogStopCallback on_stop,
                                   VoiceDialogCloseCallback on_close);

#endif // VOICE_DIALOG_H

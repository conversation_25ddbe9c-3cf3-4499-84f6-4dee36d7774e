<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AEC自动调整WebSocket测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .controls {
            margin: 20px 0;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .input-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 150px;
            font-weight: bold;
        }
        input[type="number"] {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 100px;
        }
        .toggle {
            display: inline-block;
            position: relative;
            width: 60px;
            height: 30px;
        }
        .toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 30px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 22px;
            width: 22px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #2196F3;
        }
        input:checked + .slider:before {
            transform: translateX(30px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AEC自动调整WebSocket测试</h1>
        
        <div id="status" class="status disconnected">
            未连接到WebSocket服务器
        </div>
        
        <div class="controls">
            <button id="connectBtn" class="btn-primary" onclick="connect()">连接</button>
            <button id="disconnectBtn" class="btn-danger" onclick="disconnect()" disabled>断开</button>
        </div>
        
        <div class="controls">
            <h3>📊 状态查询</h3>
            <button class="btn-primary" onclick="getAudioStatus()">获取音频状态</button>
            <button class="btn-primary" onclick="getAutoAdjust()">获取自动调整状态</button>
            <button class="btn-primary" onclick="getAecDelay()">获取AEC延时</button>
        </div>
        
        <div class="controls">
            <h3>⚙️ 自动调整控制</h3>
            <div class="input-group">
                <label>自动调整:</label>
                <label class="toggle">
                    <input type="checkbox" id="autoAdjustToggle" onchange="setAutoAdjust()">
                    <span class="slider"></span>
                </label>
                <span id="autoAdjustStatus">未知</span>
            </div>
        </div>
        
        <div class="controls">
            <h3>🔧 手动延时调整</h3>
            <div class="input-group">
                <label>AEC延时 (ms):</label>
                <input type="number" id="delayInput" value="94" min="5" max="200">
                <button class="btn-warning" onclick="setAecDelay()">设置延时</button>
            </div>
        </div>
        
        <div class="controls">
            <h3>📡 消息日志</h3>
            <button class="btn-success" onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(connected) {
            isConnected = connected;
            const statusDiv = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            if (connected) {
                statusDiv.textContent = '✅ 已连接到WebSocket服务器';
                statusDiv.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusDiv.textContent = '❌ 未连接到WebSocket服务器';
                statusDiv.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        function connect() {
            if (ws) return;
            
            ws = new WebSocket('ws://localhost:9000');
            
            ws.onopen = function() {
                log('🔗 WebSocket连接已建立');
                updateStatus(true);
                // 连接后立即获取状态
                getAudioStatus();
                getAutoAdjust();
            };
            
            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    handleMessage(data);
                } catch (e) {
                    log('❌ 解析消息失败: ' + event.data);
                }
            };
            
            ws.onclose = function() {
                log('🔌 WebSocket连接已关闭');
                updateStatus(false);
                ws = null;
            };
            
            ws.onerror = function(error) {
                log('❌ WebSocket错误: ' + error);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function sendMessage(message) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket未连接');
                return;
            }
            
            ws.send(JSON.stringify(message));
            log('📤 发送: ' + JSON.stringify(message));
        }

        function handleMessage(data) {
            log('📥 接收: ' + JSON.stringify(data));
            
            switch (data.type) {
                case 'audio_status_response':
                    updateAudioStatus(data);
                    break;
                case 'auto_adjust_response':
                    updateAutoAdjustStatus(data);
                    break;
                case 'auto_adjust_notify':
                    log(`🤖 自动调整状态变化: ${data.old_enabled} → ${data.new_enabled}`);
                    break;
                case 'aec_delay_response':
                    log(`⏱️ AEC延时: ${data.delay_ms || data.new_delay_ms}ms`);
                    break;
                case 'aec_delay_notify':
                    log(`📢 AEC延时变化通知: ${data.old_delay_ms}ms → ${data.new_delay_ms}ms`);
                    break;
                case 'error':
                    log(`❌ 错误: ${data.message}`);
                    break;
            }
        }

        function updateAudioStatus(data) {
            if (data.auto_adjust_enabled !== undefined) {
                const toggle = document.getElementById('autoAdjustToggle');
                const status = document.getElementById('autoAdjustStatus');
                toggle.checked = data.auto_adjust_enabled;
                status.textContent = data.auto_adjust_enabled ? '启用' : '禁用';
            }
            
            if (data.aec_delay_ms !== undefined) {
                document.getElementById('delayInput').value = data.aec_delay_ms;
            }
        }

        function updateAutoAdjustStatus(data) {
            if (data.enabled !== undefined || data.new_enabled !== undefined) {
                const enabled = data.enabled !== undefined ? data.enabled : data.new_enabled;
                const toggle = document.getElementById('autoAdjustToggle');
                const status = document.getElementById('autoAdjustStatus');
                toggle.checked = enabled;
                status.textContent = enabled ? '启用' : '禁用';
            }
        }

        // API函数
        function getAudioStatus() {
            sendMessage({ type: 'get_audio_status' });
        }

        function getAutoAdjust() {
            sendMessage({ type: 'get_auto_adjust' });
        }

        function getAecDelay() {
            sendMessage({ type: 'get_aec_delay' });
        }

        function setAutoAdjust() {
            const enabled = document.getElementById('autoAdjustToggle').checked;
            sendMessage({ type: 'set_auto_adjust', enabled: enabled });
        }

        function setAecDelay() {
            const delay = parseInt(document.getElementById('delayInput').value);
            if (delay < 5 || delay > 200) {
                alert('延时值必须在5-200ms之间');
                return;
            }
            sendMessage({ type: 'set_aec_delay', delay_ms: delay });
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 页面加载完成后自动连接
        window.onload = function() {
            log('🚀 页面加载完成，准备连接WebSocket...');
        };
    </script>
</body>
</html>

﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <dllmap os="!windows" dll="opencv_calib3d248" target="libopencv_calib3d.so" />
    <dllmap os="!windows" dll="opencv_contrib248" target="libopencv_contrib.so" />
    <dllmap os="!windows" dll="opencv_core248" target="libopencv_core.so" />
    <dllmap os="!windows" dll="opencv_features2d248" target="libopencv_features2d.so" />
    <dllmap os="!windows" dll="opencv_flann248" target="libopencv_flann.so" />
    <dllmap os="!windows" dll="opencv_gpu248" target="libopencv_gpu.so" />
    <dllmap os="!windows" dll="opencv_highgui248" target="libopencv_highgui.so" />
    <dllmap os="!windows" dll="opencv_imgproc248" target="libopencv_imgproc.so" />
    <dllmap os="!windows" dll="opencv_legacy248" target="libopencv_legacy.so" />
    <dllmap os="!windows" dll="opencv_ml248" target="libopencv_ml.so" />
    <dllmap os="!windows" dll="opencv_nonfree248" target="libopencv_nonfree.so" />
    <dllmap os="!windows" dll="opencv_objdetect248" target="libopencv_objdetect.so" />
    <dllmap os="!windows" dll="opencv_ocl248" target="libopencv_ocl.so" />
    <dllmap os="!windows" dll="opencv_photo248" target="libopencv_photo.so" />
    <dllmap os="!windows" dll="opencv_stitching248" target="libopencv_stitching.so" />
    <dllmap os="!windows" dll="opencv_superres248" target="libopencv_superres.so" />
    <dllmap os="!windows" dll="opencv_video248" target="libopencv_video.so" />
    <dllmap os="!windows" dll="opencv_videostab248" target="libopencv_videostab.so" />
</configuration>
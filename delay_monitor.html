<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AEC延时监控</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .metric-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .metric-unit {
            font-size: 14px;
            color: #666;
        }
        .chart-container {
            height: 300px;
            margin: 20px 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            position: relative;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            padding: 8px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .signal-good { border-left-color: #28a745; }
        .signal-weak { border-left-color: #ffc107; }
        .signal-bad { border-left-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 AEC延时监控</h1>
        
        <div id="status" class="status disconnected">
            未连接到WebSocket服务器
        </div>
        
        <div class="controls">
            <button id="connectBtn" class="btn-primary" onclick="connect()">连接</button>
            <button id="disconnectBtn" class="btn-danger" onclick="disconnect()" disabled>断开</button>
            <button class="btn-success" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <div class="container">
        <h2>📊 实时指标</h2>
        <div class="metrics">
            <div class="metric" id="estimatedDelayMetric">
                <div class="metric-label">估计延时</div>
                <div class="metric-value" id="estimatedDelay">--</div>
                <div class="metric-unit">ms</div>
            </div>
            <div class="metric" id="currentDelayMetric">
                <div class="metric-label">当前设置</div>
                <div class="metric-value" id="currentDelay">--</div>
                <div class="metric-unit">ms</div>
            </div>
            <div class="metric" id="correlationMetric">
                <div class="metric-label">相关性</div>
                <div class="metric-value" id="correlation">--</div>
                <div class="metric-unit"></div>
            </div>
            <div class="metric" id="micRmsMetric">
                <div class="metric-label">麦克风RMS</div>
                <div class="metric-value" id="micRms">--</div>
                <div class="metric-unit"></div>
            </div>
            <div class="metric" id="refRmsMetric">
                <div class="metric-label">参考RMS</div>
                <div class="metric-value" id="refRms">--</div>
                <div class="metric-unit"></div>
            </div>
            <div class="metric" id="signalStatusMetric">
                <div class="metric-label">信号状态</div>
                <div class="metric-value" id="signalStatus">--</div>
                <div class="metric-unit"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📈 延时趋势</h2>
        <div class="chart-container">
            <canvas id="delayChart" width="800" height="300"></canvas>
        </div>
    </div>

    <div class="container">
        <h2>📝 事件日志</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let delayData = [];
        let maxDataPoints = 50;

        // 图表相关
        let canvas = null;
        let ctx = null;

        function initChart() {
            canvas = document.getElementById('delayChart');
            ctx = canvas.getContext('2d');
            drawChart();
        }

        function drawChart() {
            if (!ctx) return;
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            if (delayData.length === 0) {
                ctx.fillStyle = '#666';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('等待数据...', canvas.width / 2, canvas.height / 2);
                return;
            }

            // 绘制网格
            ctx.strokeStyle = '#e0e0e0';
            ctx.lineWidth = 1;
            
            // 垂直网格线
            for (let i = 0; i <= 10; i++) {
                let x = (canvas.width / 10) * i;
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }
            
            // 水平网格线
            for (let i = 0; i <= 6; i++) {
                let y = (canvas.height / 6) * i;
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }

            // 绘制延时数据
            if (delayData.length > 1) {
                ctx.strokeStyle = '#007bff';
                ctx.lineWidth = 2;
                ctx.beginPath();
                
                let minDelay = Math.min(...delayData.map(d => d.estimated));
                let maxDelay = Math.max(...delayData.map(d => d.estimated));
                let range = maxDelay - minDelay || 1;
                
                delayData.forEach((point, index) => {
                    let x = (canvas.width / (delayData.length - 1)) * index;
                    let y = canvas.height - ((point.estimated - minDelay) / range) * canvas.height;
                    
                    if (index === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                });
                
                ctx.stroke();
                
                // 绘制当前设置线
                if (delayData.length > 0) {
                    let currentDelay = delayData[delayData.length - 1].current;
                    let y = canvas.height - ((currentDelay - minDelay) / range) * canvas.height;
                    
                    ctx.strokeStyle = '#dc3545';
                    ctx.lineWidth = 1;
                    ctx.setLineDash([5, 5]);
                    ctx.beginPath();
                    ctx.moveTo(0, y);
                    ctx.lineTo(canvas.width, y);
                    ctx.stroke();
                    ctx.setLineDash([]);
                }
            }
        }

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(connected) {
            isConnected = connected;
            const statusDiv = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            if (connected) {
                statusDiv.textContent = '✅ 已连接到WebSocket服务器';
                statusDiv.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusDiv.textContent = '❌ 未连接到WebSocket服务器';
                statusDiv.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        function updateMetrics(data) {
            if (data.type === 'delay_estimate_notify') {
                document.getElementById('estimatedDelay').textContent = data.estimated_delay_ms.toFixed(1);
                document.getElementById('currentDelay').textContent = data.current_delay_ms;
                document.getElementById('correlation').textContent = data.correlation.toFixed(3);
                document.getElementById('micRms').textContent = data.mic_rms.toFixed(1);
                document.getElementById('refRms').textContent = data.ref_rms.toFixed(1);
                
                // 添加到图表数据
                delayData.push({
                    estimated: data.estimated_delay_ms,
                    current: data.current_delay_ms,
                    timestamp: data.timestamp
                });
                
                if (delayData.length > maxDataPoints) {
                    delayData.shift();
                }
                
                drawChart();
                
                // 更新延时指标颜色
                let delayDiff = Math.abs(data.estimated_delay_ms - data.current_delay_ms);
                let delayMetric = document.getElementById('estimatedDelayMetric');
                if (delayDiff < 10) {
                    delayMetric.className = 'metric signal-good';
                } else if (delayDiff < 20) {
                    delayMetric.className = 'metric signal-weak';
                } else {
                    delayMetric.className = 'metric signal-bad';
                }
            }
            
            if (data.type === 'signal_strength_notify') {
                document.getElementById('micRms').textContent = data.mic_rms.toFixed(1);
                document.getElementById('refRms').textContent = data.ref_rms.toFixed(1);
                document.getElementById('signalStatus').textContent = data.status === 'good' ? '良好' : '较弱';
                
                // 更新信号状态颜色
                let signalMetric = document.getElementById('signalStatusMetric');
                signalMetric.className = data.status === 'good' ? 'metric signal-good' : 'metric signal-weak';
            }
        }

        function connect() {
            if (ws) return;
            
            ws = new WebSocket('ws://localhost:9000');
            
            ws.onopen = function() {
                log('🔗 WebSocket连接已建立');
                updateStatus(true);
            };
            
            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    handleMessage(data);
                } catch (e) {
                    log('❌ 解析消息失败: ' + event.data);
                }
            };
            
            ws.onclose = function() {
                log('🔌 WebSocket连接已关闭');
                updateStatus(false);
                ws = null;
            };
            
            ws.onerror = function(error) {
                log('❌ WebSocket错误: ' + error);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function handleMessage(data) {
            log('📥 ' + JSON.stringify(data));
            updateMetrics(data);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 页面加载完成后初始化
        window.onload = function() {
            initChart();
            log('🚀 延时监控页面已加载');
        };
    </script>
</body>
</html>

# 语音处理完整流程修正说明

## 🔧 修正的关键问题

### 1. **声道配置统一**
- 修正 `CHANNELS = 2`（实际硬件配置）
- 统一所有模块的声道处理逻辑
- 更新波束形成算法适配2声道

### 2. **VAD检测修正**
```c
// 修正前：错误的VAD结果获取
speex_preprocess_run(processor->preprocess_state, temp_buffer);
speex_preprocess_ctl(processor->preprocess_state, SPEEX_PREPROCESS_GET_VAD, &vad_result);

// 修正后：正确的VAD结果获取
int vad_result = speex_preprocess_run(processor->preprocess_state, temp_buffer);
```

### 3. **录音连续性保证**
- 录音在后台持续进行，不会因为状态切换而中断
- VAD检测只影响录音的开始和结束判断
- 缓冲区管理确保数据完整性

### 4. **缓冲区处理优化**
- 增强缓冲区溢出保护
- 改进数据复制逻辑，确保线程安全
- 添加详细的调试信息

### 5. **重采样器修正**
- 修正SpeexDSP重采样器的参数传递
- 改进声道转换逻辑（2ch → 1ch）
- 增强错误处理和边界检查

## 🔄 完整的语音处理流程

### 阶段1：音频捕获（连续进行）
```
ALSA捕获 → 2声道48kHz → 增益处理 → VAD检测
```

### 阶段2：VAD检测和录音控制
```
SpeexDSP VAD → 语音活动检测 → 录音状态管理
```

### 阶段3：语音缓冲区管理
```
检测到语音 → 开始录音 → 持续添加到缓冲区 → 检测静音 → 结束录音
```

### 阶段4：音频格式转换
```
48kHz 2ch → SpeexDSP重采样 → 16kHz 1ch → WAV格式
```

### 阶段5：语音识别和TTS
```
WAV文件 → HTTP上传 → Whisper识别 → TTS播报
```

## 📊 关键参数调整

### VAD参数
- 语音检测阈值：更保守的设置
- 静音帧阈值：100帧（约1秒@48kHz）
- 最小录音时长：1秒

### 音频增益
- 原始增益：6倍（降低失真）
- 波束形成增益：保持8倍
- 最终输出增益：3倍

### 缓冲区设置
- 录音缓冲区：5秒容量
- 循环缓冲区：线程安全
- 溢出保护：自动截断

## 🐛 调试信息增强

### 音频数据跟踪
- 原始音频样本
- 增益后样本
- VAD检测结果
- 重采样前后对比

### 缓冲区状态
- 缓冲区大小变化
- 录音时长统计
- 语音/静音帧计数

### 文件生成
- PCM文件保存
- WAV文件转换
- 播放命令提示

## ✅ 验证要点

1. **录音连续性**：确保录音过程中不会丢失音频数据
2. **VAD准确性**：语音检测能正确识别人声和静音
3. **格式转换**：48kHz 2ch → 16kHz 1ch 转换正确
4. **WAV文件完整性**：生成的WAV文件可以正常播放
5. **API兼容性**：转换后的音频格式符合Whisper要求

## 🔍 故障排除

### 如果WAV文件无声音
1. 检查原始音频增益是否足够
2. 验证重采样器参数设置
3. 确认声道转换逻辑正确

### 如果VAD检测不准确
1. 调整SpeexDSP VAD参数
2. 检查音频增益设置
3. 验证噪声抑制配置

### 如果录音过早结束
1. 增加静音帧阈值
2. 调整语音帧衰减逻辑
3. 检查最小录音时长设置

<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="fileTransferExtern.h" />
    <ClInclude Include="include_nlsCppSdk.h" />
    <ClInclude Include="nlsCppSdkExtern.h" />
    <ClInclude Include="nlsEventStruct.h" />
    <ClInclude Include="nlsTokenExtern.h" />
    <ClInclude Include="speechRecognizerExtern.h" />
    <ClInclude Include="speechSynthesizerExtern.h" />
    <ClInclude Include="speechTranscriberExtern.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="nlsCppSdkExtern.cpp" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{b1363115-6914-4ad4-9848-c56e57b99e11}</ProjectGuid>
    <RootNamespace>nlsCsharpSdkExtern</RootNamespace>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <CLRSupport>true</CLRSupport>
    <SupportLTL>false</SupportLTL>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <CLRSupport>false</CLRSupport>
    <SupportLTL>false</SupportLTL>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <CLRSupport>true</CLRSupport>
    <SupportLTL>false</SupportLTL>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <CLRSupport>false</CLRSupport>
    <SupportLTL>false</SupportLTL>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>false</LinkIncremental>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_NLS_SDK_SHARED_;_CRT_SECURE_NO_WARNINGS;_CONSOLE;WIN32;_DEBUG;NLSCSHARPSDKEXTERN_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>false</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(SolutionDir)..\..\nlsCppSdk\token\include;$(SolutionDir)..\..\nlsCppSdk\framework\item;$(SolutionDir)..\..\nlsCppSdk\framework\common;$(SolutionDir)..\..\nlsCppSdk\framework\feature\da;$(SolutionDir)..\..\nlsCppSdk\framework\feature\sr;$(SolutionDir)..\..\nlsCppSdk\framework\feature\st;$(SolutionDir)..\..\nlsCppSdk\framework\feature\sy</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <AdditionalLibraryDirectories>$(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Debug;$(SolutionDir)..\..\build\install\NlsSdk3.X_win32\lib\14.0\x86\Debug</AdditionalLibraryDirectories>
      <AdditionalDependencies>nlsCppSdk.lib</AdditionalDependencies>
    </Link>
    <PostBuildEvent>
      <Command>copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\pthread\pthreadVC2.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\pthread\pthreadVC2.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Debug\libcrypto-1_1.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Debug\libcrypto-1_1.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Debug\libssl-1_1.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Debug\libssl-1_1.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Debug\ssleay32.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Debug\ssleay32.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Debug\libeay32.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Debug\libeay32.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Debug\libcurld.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Debug\libcurld.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Debug\nlsCppSdk.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Debug\nlsCppSdk.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)\Win32\Debug\nlsCsharpSdkExtern.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)\Win32\Debug\nlsCsharpSdkExtern.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
md $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug\audio_files
copy $(SolutionDir)..\..\resource\audio\test0.wav $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug\audio_files
copy $(SolutionDir)..\..\resource\audio\test1.wav $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug\audio_files
copy $(SolutionDir)..\..\resource\audio\test2.wav $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug\audio_files
copy $(SolutionDir)..\..\resource\audio\test3.wav $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug\audio_files</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_NLS_SDK_SHARED_;_CRT_SECURE_NO_WARNINGS;WIN32;NDEBUG;NLSCSHARPSDKEXTERN_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>false</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(SolutionDir)..\..\nlsCppSdk\token\include;$(SolutionDir)..\..\nlsCppSdk\framework\item;$(SolutionDir)..\..\nlsCppSdk\framework\common;$(SolutionDir)..\..\nlsCppSdk\framework\feature\da;$(SolutionDir)..\..\nlsCppSdk\framework\feature\sr;$(SolutionDir)..\..\nlsCppSdk\framework\feature\st;$(SolutionDir)..\..\nlsCppSdk\framework\feature\sy</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <AdditionalLibraryDirectories>$(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Release;$(SolutionDir)..\..\build\install\NlsSdk3.X_win32\lib\14.0\x86\Release</AdditionalLibraryDirectories>
      <AdditionalDependencies>nlsCppSdk.lib</AdditionalDependencies>
    </Link>
    <PostBuildEvent>
      <Command>copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\pthread\pthreadVC2.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\pthread\pthreadVC2.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Release\libcrypto-1_1.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Release\libcrypto-1_1.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Release\libssl-1_1.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Release\libssl-1_1.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Release\ssleay32.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Release\ssleay32.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Release\libeay32.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Release\libeay32.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Release\libcurl.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x86\Release\libcurl.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Release\nlsCppSdk.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\build\build_win32\nlsCppSdk\Win32\Release\nlsCppSdk.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)\Win32\Release\nlsCsharpSdkExtern.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)\Win32\Release\nlsCsharpSdkExtern.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
md $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release\audio_files
copy $(SolutionDir)..\..\resource\audio\test0.wav $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release\audio_files
copy $(SolutionDir)..\..\resource\audio\test1.wav $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release\audio_files
copy $(SolutionDir)..\..\resource\audio\test2.wav $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release\audio_files
copy $(SolutionDir)..\..\resource\audio\test3.wav $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release\audio_files

</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_NLS_SDK_SHARED_;_CRT_SECURE_NO_WARNINGS;_DEBUG;_CONSOLE;NLSCSHARPSDKEXTERN_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>false</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(SolutionDir)..\..\nlsCppSdk\token\include;$(SolutionDir)..\..\nlsCppSdk\framework\item;$(SolutionDir)..\..\nlsCppSdk\framework\common;$(SolutionDir)..\..\nlsCppSdk\framework\feature\da;$(SolutionDir)..\..\nlsCppSdk\framework\feature\sr;$(SolutionDir)..\..\nlsCppSdk\framework\feature\st;$(SolutionDir)..\..\nlsCppSdk\framework\feature\sy</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <AdditionalLibraryDirectories>$(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Debug;$(SolutionDir)..\..\build\install\NlsSdk3.X_win64\lib\14.0\x64\Debug</AdditionalLibraryDirectories>
      <AdditionalDependencies>nlsCppSdk.lib</AdditionalDependencies>
    </Link>
    <ProjectReference>
      <UseLibraryDependencyInputs>false</UseLibraryDependencyInputs>
    </ProjectReference>
    <PostBuildEvent>
      <Command>copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\pthread\pthreadVC2.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\pthread\pthreadVC2.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Debug\libcrypto-1_1-x64.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Debug\libcrypto-1_1-x64.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Debug\libssl-1_1-x64.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Debug\libssl-1_1-x64.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Debug\ssleay32.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Debug\ssleay32.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Debug\libeay32.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Debug\libeay32.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Debug\libcurld.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Debug\libcurld.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Debug\nlsCppSdk.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Debug\nlsCppSdk.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)\x64\Debug\nlsCsharpSdkExtern.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
copy $(SolutionDir)\x64\Debug\nlsCsharpSdkExtern.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug
md $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug\audio_files
copy $(SolutionDir)..\..\resource\audio\test0.wav $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug\audio_files
copy $(SolutionDir)..\..\resource\audio\test1.wav $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug\audio_files
copy $(SolutionDir)..\..\resource\audio\test2.wav $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug\audio_files
copy $(SolutionDir)..\..\resource\audio\test3.wav $(SolutionDir)..\nlsCsharpSdkDemo\bin\Debug\audio_files</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_NLS_SDK_SHARED_;_CRT_SECURE_NO_WARNINGS;_CONSOLE;NDEBUG;NLSCSHARPSDKEXTERN_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>false</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(SolutionDir)..\..\nlsCppSdk\token\include;$(SolutionDir)..\..\nlsCppSdk\framework\item;$(SolutionDir)..\..\nlsCppSdk\framework\common;$(SolutionDir)..\..\nlsCppSdk\framework\feature\da;$(SolutionDir)..\..\nlsCppSdk\framework\feature\sr;$(SolutionDir)..\..\nlsCppSdk\framework\feature\st;$(SolutionDir)..\..\nlsCppSdk\framework\feature\sy</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <AdditionalLibraryDirectories>$(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Release;$(SolutionDir)..\..\build\install\NlsSdk3.X_win64\lib\14.0\x64\Release</AdditionalLibraryDirectories>
      <AdditionalDependencies>nlsCppSdk.lib</AdditionalDependencies>
    </Link>
    <PostBuildEvent>
      <Command>copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\pthread\pthreadVC2.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\pthread\pthreadVC2.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Release\libcrypto-1_1-x64.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Release\libcrypto-1_1-x64.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Release\libssl-1_1-x64.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Release\libssl-1_1-x64.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Release\ssleay32.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Release\ssleay32.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Release\libeay32.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Release\libeay32.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Release\libcurl.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\nlsCppSdk\win\libs_14.0\x64\Release\libcurl.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Release\nlsCppSdk.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)..\..\build\build_win64\nlsCppSdk\x64\Release\nlsCppSdk.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)\x64\Release\nlsCsharpSdkExtern.lib  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
copy $(SolutionDir)\x64\Release\nlsCsharpSdkExtern.dll  $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release
md $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release\audio_files
copy $(SolutionDir)..\..\resource\audio\test0.wav $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release\audio_files
copy $(SolutionDir)..\..\resource\audio\test1.wav $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release\audio_files
copy $(SolutionDir)..\..\resource\audio\test2.wav $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release\audio_files
copy $(SolutionDir)..\..\resource\audio\test3.wav $(SolutionDir)..\nlsCsharpSdkDemo\bin\Release\audio_files</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets" />
</Project>
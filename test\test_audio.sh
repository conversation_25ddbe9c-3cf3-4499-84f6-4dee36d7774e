#!/bin/bash

echo "=== Audio Device Test Script ==="

# 检查设备状态
echo "1. Checking device status..."
lsof /dev/snd/* 2>/dev/null | head -10

echo
echo "2. Listing audio devices..."
arecord -l
echo
aplay -l

echo
echo "3. Testing USB microphone recording..."
echo "Recording 5 seconds from USB microphone..."
timeout 5s arecord -D hw:2,0 -f S32_LE -r 96000 -c 2 test_record.raw

if [ -f test_record.raw ]; then
    echo "Recording successful! File size: $(stat -c%s test_record.raw) bytes"
    
    echo
    echo "4. Testing playback..."
    echo "Playing back recorded audio..."
    aplay -D default -f S32_LE -r 96000 -c 2 test_record.raw
    
    echo
    echo "5. Testing format conversion and loopback..."
    echo "Converting and playing in real-time..."
    echo "Press Ctrl+C to stop"
    
    # 实时录音和播放，带格式转换
    arecord -D hw:2,0 -f S32_LE -r 96000 -c 2 | \
    sox -t raw -r 96000 -c 2 -e signed -b 32 - -t raw -r 48000 -c 2 -e signed -b 16 - gain 10 | \
    aplay -D default -f S16_LE -r 48000 -c 2
    
else
    echo "Recording failed!"
    echo "Trying alternative recording methods..."
    
    echo "Trying with plughw:2,0..."
    timeout 5s arecord -D plughw:2,0 -f S16_LE -r 48000 -c 2 test_record2.raw
    
    if [ -f test_record2.raw ]; then
        echo "Alternative recording successful!"
        aplay -D default -f S16_LE -r 48000 -c 2 test_record2.raw
    else
        echo "All recording methods failed!"
    fi
fi

echo
echo "6. Cleanup..."
rm -f test_record.raw test_record2.raw

echo "Test completed."

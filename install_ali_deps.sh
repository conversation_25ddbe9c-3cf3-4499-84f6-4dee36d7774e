#!/bin/bash

echo "🚀 Installing dependencies for Ali ASR integration..."

# 更新包管理器
echo "📦 Updating package manager..."
sudo apt update

# 安装json-c库
echo "📦 Installing json-c library..."
sudo apt install -y libjson-c-dev

# 验证安装
echo "✅ Verifying installations..."

# 检查json-c
if pkg-config --exists json-c; then
    echo "✅ json-c found: $(pkg-config --modversion json-c)"
else
    echo "❌ json-c not found"
    exit 1
fi

# 检查其他必要的库
echo "🔍 Checking other dependencies..."

if pkg-config --exists opus; then
    echo "✅ opus found: $(pkg-config --modversion opus)"
else
    echo "❌ opus not found"
    exit 1
fi

if pkg-config --exists libcurl; then
    echo "✅ libcurl found: $(pkg-config --modversion libcurl)"
else
    echo "❌ libcurl not found"
    exit 1
fi

if pkg-config --exists alsa; then
    echo "✅ alsa found: $(pkg-config --modversion alsa)"
else
    echo "❌ alsa not found"
    exit 1
fi

if pkg-config --exists speexdsp; then
    echo "✅ speexdsp found: $(pkg-config --modversion speexdsp)"
else
    echo "❌ speexdsp not found"
    exit 1
fi

echo "🎉 All dependencies installed successfully!"
echo ""
echo "📋 Summary:"
echo "  - json-c: $(pkg-config --modversion json-c)"
echo "  - opus: $(pkg-config --modversion opus)"
echo "  - libcurl: $(pkg-config --modversion libcurl)"
echo "  - alsa: $(pkg-config --modversion alsa)"
echo "  - speexdsp: $(pkg-config --modversion speexdsp)"
echo ""
echo "🔨 You can now build the project with:"
echo "  mkdir -p build && cd build"
echo "  cmake .."
echo "  make"

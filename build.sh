#!/bin/bash

# 4声道麦克风阵列音频处理器构建脚本
# 适用于Ubuntu/Debian系统

set -e  # 遇到错误立即退出

echo "=== 4声道麦克风阵列音频处理器构建脚本 ==="
echo

# 检查系统
echo "检查系统环境..."
if ! command -v cmake &> /dev/null; then
    echo "错误: 未找到cmake，请先安装: sudo apt install cmake"
    exit 1
fi

if ! command -v gcc &> /dev/null; then
    echo "错误: 未找到gcc，请先安装: sudo apt install build-essential"
    exit 1
fi

if ! command -v pkg-config &> /dev/null; then
    echo "错误: 未找到pkg-config，请先安装: sudo apt install pkg-config"
    exit 1
fi

# 检查SpeexDSP库
echo "检查SpeexDSP库..."
if ! pkg-config --exists speexdsp; then
    echo "错误: 未找到SpeexDSP库"
    echo "请安装: sudo apt install libspeexdsp-dev"
    echo "或从源码编译SpeexDSP库"
    exit 1
fi

SPEEXDSP_VERSION=$(pkg-config --modversion speexdsp)
echo "找到SpeexDSP版本: $SPEEXDSP_VERSION"

# 创建构建目录
echo "创建构建目录..."
if [ -d "build" ]; then
    echo "清理现有构建目录..."
    rm -rf build
fi

mkdir build
cd build

# 配置项目
echo "配置CMake项目..."
cmake .. -DCMAKE_BUILD_TYPE=Release

# 编译项目
echo "编译项目..."
make -j$(nproc)

# 检查编译结果
if [ -f "mic_dev" ]; then
    echo
    echo "=== 编译成功! ==="
    echo "可执行文件: $(pwd)/mic_dev"
    echo
    echo "使用方法:"
    echo "  ./mic_dev <input_4ch.pcm> <output_1ch.pcm> [reference.pcm]"
    echo
    echo "示例:"
    echo "  ./mic_dev /home/<USER>/test20s.pcm output.pcm"
    echo
    
    # 显示文件信息
    ls -lh mic_dev
    
    # 测试程序是否能正常运行
    echo "测试程序..."
    if ./mic_dev 2>&1 | grep -q "Usage:"; then
        echo "程序测试通过!"
    else
        echo "警告: 程序可能存在问题"
    fi
    
else
    echo "错误: 编译失败，未找到可执行文件"
    exit 1
fi

echo
echo "构建完成!"

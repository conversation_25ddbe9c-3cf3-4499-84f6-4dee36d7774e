﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{255E4DB8-BD63-3F0A-BB96-3B77F5963BD1}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <Keyword>Win32Proj</Keyword>
    <Platform>x64</Platform>
    <ProjectName>event_openssl_shared</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v141</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">event_openssl_shared.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">event_openssl</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">event_openssl</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">event_openssl_shared.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">event_openssl</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">event_openssl</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\compat;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable;D:\Anaconda3\Library\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\.\WIN32-Code;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <CompileAs>CompileAsC</CompileAs>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>WIN32;_WINDOWS;HAVE_CONFIG_H;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;TINYTEST_LOCAL;CMAKE_INTDIR="Debug";event_openssl_shared_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;HAVE_CONFIG_H;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;TINYTEST_LOCAL;CMAKE_INTDIR=\"Debug\";event_openssl_shared_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\compat;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable;D:\Anaconda3\Library\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\.\WIN32-Code;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\compat;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable;D:\Anaconda3\Library\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\.\WIN32-Code;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>ws2_32.lib;shell32.lib;advapi32.lib;lib\Debug\event_core.lib;D:\Anaconda3\Library\lib\libssl.lib;D:\Anaconda3\Library\lib\libcrypto.lib;ws2_32.lib;shell32.lib;advapi32.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/build/lib/Debug/event_openssl.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/build/bin/Debug/event_openssl.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\compat;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable;D:\Anaconda3\Library\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\.\WIN32-Code;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <CompileAs>CompileAsC</CompileAs>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>WIN32;_WINDOWS;HAVE_CONFIG_H;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;TINYTEST_LOCAL;CMAKE_INTDIR="Debug";event_openssl_shared_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;HAVE_CONFIG_H;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;TINYTEST_LOCAL;CMAKE_INTDIR=\"Debug\";event_openssl_shared_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\compat;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable;D:\Anaconda3\Library\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\.\WIN32-Code;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\compat;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable;D:\Anaconda3\Library\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\.\WIN32-Code;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>ws2_32.lib;shell32.lib;advapi32.lib;lib\Debug\event_core.lib;D:\Anaconda3\Library\lib\libssl.lib;D:\Anaconda3\Library\lib\libcrypto.lib;ws2_32.lib;shell32.lib;advapi32.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/build/lib/Debug/event_openssl.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/build/bin/Debug/event_openssl.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\compat;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable;D:\Anaconda3\Library\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\.\WIN32-Code;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <CompileAs>CompileAsC</CompileAs>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;HAVE_CONFIG_H;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;TINYTEST_LOCAL;CMAKE_INTDIR="Release";event_openssl_shared_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;HAVE_CONFIG_H;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;TINYTEST_LOCAL;CMAKE_INTDIR=\"Release\";event_openssl_shared_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\compat;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable;D:\Anaconda3\Library\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\.\WIN32-Code;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\compat;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable;D:\Anaconda3\Library\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\.\WIN32-Code;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>ws2_32.lib;shell32.lib;advapi32.lib;lib\Release\event_core.lib;D:\Anaconda3\Library\lib\libssl.lib;D:\Anaconda3\Library\lib\libcrypto.lib;ws2_32.lib;shell32.lib;advapi32.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/build/lib/Release/event_openssl.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/build/bin/Release/event_openssl.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\compat;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable;D:\Anaconda3\Library\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\.\WIN32-Code;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <CompileAs>CompileAsC</CompileAs>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;HAVE_CONFIG_H;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;TINYTEST_LOCAL;CMAKE_INTDIR="Release";event_openssl_shared_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;HAVE_CONFIG_H;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;TINYTEST_LOCAL;CMAKE_INTDIR=\"Release\";event_openssl_shared_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\compat;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable;D:\Anaconda3\Library\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\.\WIN32-Code;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\compat;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable;D:\Anaconda3\Library\include;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\.\WIN32-Code;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>ws2_32.lib;shell32.lib;advapi32.lib;lib\Release\event_core.lib;D:\Anaconda3\Library\lib\libssl.lib;D:\Anaconda3\Library\lib\libcrypto.lib;ws2_32.lib;shell32.lib;advapi32.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/build/lib/Release/event_openssl.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/build/bin/Release/event_openssl.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\CMakeLists.txt">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/CMakeLists.txt</Message>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Building Custom Rule C:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\cmake-3.17.0-rc1-win64-x64\bin\cmake.exe -SC:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable -BC:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/build --check-stamp-file C:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">setlocal
D:\cmake-3.17.0-rc1-win64-x64\bin\cmake.exe -SC:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable -BC:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/build --check-stamp-file C:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\3.17.0-rc1\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\3.17.0-rc1\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\3.17.0-rc1\CMakeSystem.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_AFUNIX_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_ERRNO_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_FCNTL_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_INTTYPES_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_IO_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_MEMORY_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SIGNAL_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STDARG_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STDDEF_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STDINT_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STDLIB_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STRING_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_PARAM_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_STAT_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_SYSCTL_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_TIME_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_TYPES_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_TIME_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_UNISTD_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_WINSOCK2_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_WS2TCPIP_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_FD_MASK.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_SA_FAMILY_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_ADDRINFO.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_IN6_ADDR.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_LINGER.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_SOCKADDR_IN6.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_SOCKADDR_STORAGE.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_SOCKADDR_UN.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINT16_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINT32_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINT64_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINT8_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINTPTR_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_INT.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_LONG.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_LONG_LONG.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_OFF_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_PID_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_PTHREAD_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SHORT.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SIZE_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SOCKLEN_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SSIZE_T_LOWER.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SSIZE_T_UPPER.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_TIME_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_UNSIGNED.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_UNSIGNED_INT.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_VOID_P.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\AddCompilerFlags.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\AddEventLibrary.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\CheckConstExists.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\CheckFileOffsetBits.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\CheckFunctionKeywords.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\CheckPrototypeDefinition.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\LibeventConfig.cmake.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\LibeventConfigVersion.cmake.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\Macros.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\Uninstall.cmake.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\VersionViaGit.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\evconfig-private.h.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\event-config.h.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\libevent.pc.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\libevent_core.pc.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\libevent_extra.pc.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\libevent_openssl.pc.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCCompiler.cmake.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCCompilerABI.c;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCInformation.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCommonLanguageInclude.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCompilerIdDetection.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeConfigurableFile.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCompileFeatures.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCompilerABI.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCompilerId.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineRCCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineSystem.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeFindBinUtils.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeGenericSystem.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeInitializeConfigs.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeLanguageInformation.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeParseArguments.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeParseImplicitLinkInfo.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeRCCompiler.cmake.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeRCInformation.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeSystem.cmake.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeSystemSpecificInformation.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeSystemSpecificInitialize.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeTestCCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeTestCompilerCommon.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeTestRCCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CTest.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CTestTargets.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CTestUseLaunchers.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckCCompilerFlag.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckCSourceCompiles.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckCXXSourceCompiles.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckIncludeFile.c.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckIncludeFile.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckIncludeFileCXX.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckIncludeFiles.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckStructHasMember.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckSymbolExists.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckTypeSize.c.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckTypeSize.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckVariableExists.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\MSVC-C.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\TI-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CompilerId\VS-10.vcxproj.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\DartConfiguration.tcl.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindGit.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindOpenSSL.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindPackageHandleStandardArgs.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindPackageMessage.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindPythonInterp.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindZLIB.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Internal\FeatureTesting.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Platform\Windows-MSVC-C.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Platform\Windows-MSVC.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Platform\Windows.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Platform\WindowsPaths.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\SelectLibraryConfigurations.cmake;%(AdditionalInputs)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\3.17.0-rc1\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\3.17.0-rc1\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\3.17.0-rc1\CMakeSystem.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_AFUNIX_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_ERRNO_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_FCNTL_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_INTTYPES_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_IO_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_MEMORY_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SIGNAL_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STDARG_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STDDEF_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STDINT_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STDLIB_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STRING_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_PARAM_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_STAT_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_SYSCTL_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_TIME_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_TYPES_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_TIME_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_UNISTD_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_WINSOCK2_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_WS2TCPIP_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_FD_MASK.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_SA_FAMILY_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_ADDRINFO.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_IN6_ADDR.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_LINGER.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_SOCKADDR_IN6.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_SOCKADDR_STORAGE.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_SOCKADDR_UN.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINT16_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINT32_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINT64_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINT8_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINTPTR_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_INT.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_LONG.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_LONG_LONG.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_OFF_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_PID_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_PTHREAD_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SHORT.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SIZE_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SOCKLEN_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SSIZE_T_LOWER.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SSIZE_T_UPPER.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_TIME_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_UNSIGNED.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_UNSIGNED_INT.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_VOID_P.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\AddCompilerFlags.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\AddEventLibrary.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\CheckConstExists.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\CheckFileOffsetBits.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\CheckFunctionKeywords.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\CheckPrototypeDefinition.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\LibeventConfig.cmake.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\LibeventConfigVersion.cmake.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\Macros.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\Uninstall.cmake.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\VersionViaGit.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\evconfig-private.h.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\event-config.h.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\libevent.pc.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\libevent_core.pc.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\libevent_extra.pc.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\libevent_openssl.pc.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCCompiler.cmake.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCCompilerABI.c;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCInformation.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCommonLanguageInclude.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCompilerIdDetection.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeConfigurableFile.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCompileFeatures.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCompilerABI.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCompilerId.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineRCCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineSystem.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeFindBinUtils.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeGenericSystem.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeInitializeConfigs.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeLanguageInformation.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeParseArguments.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeParseImplicitLinkInfo.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeRCCompiler.cmake.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeRCInformation.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeSystem.cmake.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeSystemSpecificInformation.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeSystemSpecificInitialize.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeTestCCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeTestCompilerCommon.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeTestRCCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CTest.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CTestTargets.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CTestUseLaunchers.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckCCompilerFlag.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckCSourceCompiles.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckCXXSourceCompiles.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckIncludeFile.c.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckIncludeFile.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckIncludeFileCXX.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckIncludeFiles.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckStructHasMember.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckSymbolExists.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckTypeSize.c.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckTypeSize.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckVariableExists.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\MSVC-C.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\TI-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CompilerId\VS-10.vcxproj.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\DartConfiguration.tcl.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindGit.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindOpenSSL.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindPackageHandleStandardArgs.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindPackageMessage.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindPythonInterp.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindZLIB.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Internal\FeatureTesting.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Platform\Windows-MSVC-C.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Platform\Windows-MSVC.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Platform\Windows.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Platform\WindowsPaths.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\SelectLibraryConfigurations.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\generate.stamp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/CMakeLists.txt</Message>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Building Custom Rule C:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\cmake-3.17.0-rc1-win64-x64\bin\cmake.exe -SC:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable -BC:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/build --check-stamp-file C:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">setlocal
D:\cmake-3.17.0-rc1-win64-x64\bin\cmake.exe -SC:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable -BC:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/build --check-stamp-file C:/Users/<USER>/Desktop/libevent-2.1.12-stable/libevent-2.1.12-stable/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\3.17.0-rc1\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\3.17.0-rc1\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\3.17.0-rc1\CMakeSystem.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_AFUNIX_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_ERRNO_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_FCNTL_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_INTTYPES_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_IO_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_MEMORY_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SIGNAL_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STDARG_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STDDEF_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STDINT_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STDLIB_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STRING_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_PARAM_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_STAT_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_SYSCTL_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_TIME_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_TYPES_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_TIME_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_UNISTD_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_WINSOCK2_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_WS2TCPIP_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_FD_MASK.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_SA_FAMILY_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_ADDRINFO.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_IN6_ADDR.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_LINGER.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_SOCKADDR_IN6.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_SOCKADDR_STORAGE.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_SOCKADDR_UN.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINT16_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINT32_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINT64_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINT8_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINTPTR_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_INT.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_LONG.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_LONG_LONG.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_OFF_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_PID_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_PTHREAD_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SHORT.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SIZE_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SOCKLEN_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SSIZE_T_LOWER.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SSIZE_T_UPPER.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_TIME_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_UNSIGNED.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_UNSIGNED_INT.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_VOID_P.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\AddCompilerFlags.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\AddEventLibrary.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\CheckConstExists.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\CheckFileOffsetBits.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\CheckFunctionKeywords.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\CheckPrototypeDefinition.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\LibeventConfig.cmake.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\LibeventConfigVersion.cmake.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\Macros.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\Uninstall.cmake.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\VersionViaGit.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\evconfig-private.h.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\event-config.h.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\libevent.pc.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\libevent_core.pc.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\libevent_extra.pc.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\libevent_openssl.pc.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCCompiler.cmake.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCCompilerABI.c;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCInformation.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCommonLanguageInclude.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCompilerIdDetection.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeConfigurableFile.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCompileFeatures.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCompilerABI.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCompilerId.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineRCCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineSystem.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeFindBinUtils.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeGenericSystem.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeInitializeConfigs.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeLanguageInformation.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeParseArguments.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeParseImplicitLinkInfo.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeRCCompiler.cmake.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeRCInformation.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeSystem.cmake.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeSystemSpecificInformation.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeSystemSpecificInitialize.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeTestCCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeTestCompilerCommon.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeTestRCCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CTest.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CTestTargets.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CTestUseLaunchers.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckCCompilerFlag.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckCSourceCompiles.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckCXXSourceCompiles.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckIncludeFile.c.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckIncludeFile.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckIncludeFileCXX.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckIncludeFiles.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckStructHasMember.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckSymbolExists.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckTypeSize.c.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckTypeSize.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckVariableExists.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\MSVC-C.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\TI-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CompilerId\VS-10.vcxproj.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\DartConfiguration.tcl.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindGit.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindOpenSSL.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindPackageHandleStandardArgs.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindPackageMessage.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindPythonInterp.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindZLIB.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Internal\FeatureTesting.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Platform\Windows-MSVC-C.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Platform\Windows-MSVC.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Platform\Windows.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Platform\WindowsPaths.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\SelectLibraryConfigurations.cmake;%(AdditionalInputs)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\3.17.0-rc1\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\3.17.0-rc1\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\3.17.0-rc1\CMakeSystem.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_AFUNIX_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_ERRNO_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_FCNTL_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_INTTYPES_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_IO_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_MEMORY_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SIGNAL_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STDARG_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STDDEF_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STDINT_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STDLIB_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_STRING_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_PARAM_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_STAT_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_SYSCTL_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_TIME_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_SYS_TYPES_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_TIME_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_UNISTD_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_WINSOCK2_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckIncludeFiles\EVENT__HAVE_WS2TCPIP_H.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_FD_MASK.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_SA_FAMILY_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_ADDRINFO.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_IN6_ADDR.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_LINGER.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_SOCKADDR_IN6.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_SOCKADDR_STORAGE.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_STRUCT_SOCKADDR_UN.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINT16_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINT32_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINT64_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINT8_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__HAVE_UINTPTR_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_INT.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_LONG.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_LONG_LONG.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_OFF_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_PID_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_PTHREAD_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SHORT.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SIZE_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SOCKLEN_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SSIZE_T_LOWER.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_SSIZE_T_UPPER.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_TIME_T.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_UNSIGNED.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_UNSIGNED_INT.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\CheckTypeSize\EVENT__SIZEOF_VOID_P.c;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\AddCompilerFlags.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\AddEventLibrary.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\CheckConstExists.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\CheckFileOffsetBits.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\CheckFunctionKeywords.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\CheckPrototypeDefinition.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\LibeventConfig.cmake.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\LibeventConfigVersion.cmake.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\Macros.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\Uninstall.cmake.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\cmake\VersionViaGit.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\evconfig-private.h.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\event-config.h.cmake;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\libevent.pc.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\libevent_core.pc.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\libevent_extra.pc.in;C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\libevent_openssl.pc.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCCompiler.cmake.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCCompilerABI.c;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCInformation.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCommonLanguageInclude.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeCompilerIdDetection.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeConfigurableFile.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCompileFeatures.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCompilerABI.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineCompilerId.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineRCCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeDetermineSystem.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeFindBinUtils.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeGenericSystem.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeInitializeConfigs.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeLanguageInformation.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeParseArguments.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeParseImplicitLinkInfo.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeRCCompiler.cmake.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeRCInformation.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeSystem.cmake.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeSystemSpecificInformation.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeSystemSpecificInitialize.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeTestCCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeTestCompilerCommon.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CMakeTestRCCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CTest.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CTestTargets.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CTestUseLaunchers.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckCCompilerFlag.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckCSourceCompiles.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckCXXSourceCompiles.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckIncludeFile.c.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckIncludeFile.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckIncludeFileCXX.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckIncludeFiles.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckStructHasMember.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckSymbolExists.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckTypeSize.c.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckTypeSize.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CheckVariableExists.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\MSVC-C.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\TI-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\CompilerId\VS-10.vcxproj.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\DartConfiguration.tcl.in;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindGit.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindOpenSSL.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindPackageHandleStandardArgs.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindPackageMessage.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindPythonInterp.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\FindZLIB.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Internal\FeatureTesting.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Platform\Windows-MSVC-C.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Platform\Windows-MSVC.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Platform\Windows.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\Platform\WindowsPaths.cmake;D:\cmake-3.17.0-rc1-win64-x64\share\cmake-3.17\Modules\SelectLibraryConfigurations.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\generate.stamp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\bufferevent_openssl.c" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\ZERO_CHECK.vcxproj">
      <Project>{56DBCEC6-7D42-3319-A06E-57F132AB367E}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\libevent-2.1.12-stable\libevent-2.1.12-stable\build\event_core_shared.vcxproj">
      <Project>{6CD1F079-1AAE-3A89-9A52-D09FDEA8C779}</Project>
      <Name>event_core_shared</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
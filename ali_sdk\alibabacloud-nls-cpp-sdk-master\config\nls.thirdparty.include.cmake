
#jsoncpp
set(J<PERSON><PERSON>PP1_URL  ${CMAKE_CURRENT_SOURCE_DIR}/jsoncpp-1.9.4.zip)
set(JSONCPP1_URL_HASH 4733a5a68db4ef8b7754c8b6c8956973)
set(JSONCPP0_URL  ${CMAKE_CURRENT_SOURCE_DIR}/jsoncpp-0.y.z.zip)
set(JSONCPP0_URL_HASH 8da4bafedec6d31886cb9d9c6606638f)

#ogg
set(OGG_URL ${CMAKE_CURRENT_SOURCE_DIR}/libogg-1.3.5.tar.gz)
set(OGG_URL_HASH 3267127fe8d7ba77d3e00cb9d7ad578d)

#opus
set(OPUS_URL ${CMAKE_CURRENT_SOURCE_DIR}/opus-1.2.1a.tar.gz)
set(OPUS_URL_HASH 37adec10c59e90659251572a5fe20e72)

#uuid
set(UUID_URL ${CMAKE_CURRENT_SOURCE_DIR}/libuuid-1.0.3.tar.gz)
set(UUID_URL_HASH d44d866d06286c08ba0846aba1086d68)

#openssl
set(OPENSSL_URL ${CMAKE_CURRENT_SOURCE_DIR}/openssl-1.1.1l.tar.gz)
set(OPENSSL_URL_HASH ac0d4387f3ba0ad741b0580dd45f6ff3)

#log4cpp
set(LOG4CPP_URL ${CMAKE_CURRENT_SOURCE_DIR}/log4cpp-1.1.3b.tar.gz)
set(LOG4CPP_URL_HASH 500140e1fe5d9b59634d16e42d708781)

#libevent
set(LIBEVENT_URL ${CMAKE_CURRENT_SOURCE_DIR}/libevent-2.1.12-stable.tar.gz)
set(LIBEVENT_URL_HASH b5333f021f880fe76490d8a799cd79f4)

#curl
set(CURL_URL ${CMAKE_CURRENT_SOURCE_DIR}/curl-7.79.1.tar.gz)
set(CURL_URL_HASH 2840cca526ec80353fa334d28d7aa581)


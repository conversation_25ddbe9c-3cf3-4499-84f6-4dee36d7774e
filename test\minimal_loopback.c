#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <alsa/asoundlib.h>
#include <signal.h>
#include <unistd.h>

// 全局变量
static volatile int g_running = 1;

// 信号处理函数
void signal_handler(int sig) {
    printf("\nReceived signal %d, stopping...\n", sig);
    g_running = 0;
}

// 打印错误信息
void print_error(const char *func, int err) {
    fprintf(stderr, "ALSA error in %s: %s\n", func, snd_strerror(err));
}

// 查找USB麦克风设备
int find_usb_mic(char *device_name, size_t size) {
    snd_ctl_t *handle;
    snd_ctl_card_info_t *info;
    int card = -1;
    
    snd_ctl_card_info_alloca(&info);
    
    while (snd_card_next(&card) >= 0 && card >= 0) {
        char name[32];
        snprintf(name, sizeof(name), "hw:%d", card);
        
        if (snd_ctl_open(&handle, name, 0) < 0) continue;
        if (snd_ctl_card_info(handle, info) < 0) {
            snd_ctl_close(handle);
            continue;
        }
        
        const char *card_name = snd_ctl_card_info_get_name(info);
        printf("Found card %d: %s\n", card, card_name);
        
        if (strstr(card_name, "USB") || strstr(card_name, "Audio") || card == 2) {
            snprintf(device_name, size, "hw:%d", card);
            printf("Using USB card device: %s (%s)\n", device_name, card_name);
            snd_ctl_close(handle);
            return 0;
        }
        
        snd_ctl_close(handle);
    }
    
    // 如果没找到，使用默认设备
    snprintf(device_name, size, "default");
    printf("No USB device found, using default\n");
    return 0;
}

// 主函数
int main(int argc, char *argv[]) {
    int err;
    char capture_device[256] = "default";
    char playback_device[256] = "default";
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    printf("=== Minimal ALSA Audio Loopback ===\n");

    // 直接使用已知的USB麦克风设备
    strcpy(capture_device, "hw:2,0");  // 根据之前的输出，USB Audio在hw:2,0
    printf("Using USB microphone: %s\n", capture_device);
    
    // 打开录音设备
    snd_pcm_t *capture_handle;
    if ((err = snd_pcm_open(&capture_handle, capture_device, SND_PCM_STREAM_CAPTURE, 0)) < 0) {
        print_error("snd_pcm_open capture", err);
        return 1;
    }
    
    // 打开播放设备
    snd_pcm_t *playback_handle;
    if ((err = snd_pcm_open(&playback_handle, playback_device, SND_PCM_STREAM_PLAYBACK, 0)) < 0) {
        print_error("snd_pcm_open playback", err);
        snd_pcm_close(capture_handle);
        return 1;
    }
    
    // 使用snd_pcm_set_params简化配置 - 录音设备
    // 参数：handle, format, access, channels, rate, soft_resample, latency_us
    unsigned int capture_rate = 96000;  // USB麦克风的原生采样率
    unsigned int capture_latency = 500000;  // 500ms延迟，减少underrun
    
    if ((err = snd_pcm_set_params(capture_handle,
                                  SND_PCM_FORMAT_S32_LE,  // USB麦克风支持的格式
                                  SND_PCM_ACCESS_RW_INTERLEAVED,
                                  2,  // 2声道
                                  capture_rate,
                                  1,  // 允许重采样
                                  capture_latency)) < 0) {
        print_error("snd_pcm_set_params capture", err);
        goto cleanup;
    }
    
    printf("Capture configured: S32_LE, 2ch, %uHz, latency=%uus\n", 
           capture_rate, capture_latency);
    
    // 配置播放设备
    unsigned int playback_rate = 48000;  // 常见的播放采样率
    unsigned int playback_latency = 500000;  // 500ms延迟
    
    if ((err = snd_pcm_set_params(playback_handle,
                                  SND_PCM_FORMAT_S16_LE,  // 常见的播放格式
                                  SND_PCM_ACCESS_RW_INTERLEAVED,
                                  2,  // 2声道
                                  playback_rate,
                                  1,  // 允许重采样
                                  playback_latency)) < 0) {
        print_error("snd_pcm_set_params playback", err);
        goto cleanup;
    }
    
    printf("Playback configured: S16_LE, 2ch, %uHz, latency=%uus\n", 
           playback_rate, playback_latency);
    
    // 计算降采样比例
    int downsample_ratio = capture_rate / playback_rate;  // 96000/48000 = 2
    printf("Downsample ratio: %d:1\n", downsample_ratio);
    
    // 计算帧大小 - 使用较小的帧以减少延迟
    snd_pcm_uframes_t frames_per_period = 1024;
    
    // 分配缓冲区
    int32_t *capture_buffer = malloc(frames_per_period * 2 * sizeof(int32_t));
    int16_t *playback_buffer = malloc(frames_per_period * 2 * sizeof(int16_t));
    
    if (!capture_buffer || !playback_buffer) {
        printf("Failed to allocate buffers\n");
        goto cleanup;
    }
    
    printf("Starting audio loopback with %lu frames per period...\n", frames_per_period);
    printf("Press Ctrl+C to stop\n");
    
    // 主循环
    int frame_count = 0;
    while (g_running) {
        // 读取数据
        snd_pcm_sframes_t frames_read = snd_pcm_readi(capture_handle, capture_buffer, frames_per_period);
        
        if (frames_read == -EPIPE) {
            printf("Capture underrun, recovering...\n");
            snd_pcm_prepare(capture_handle);
            continue;
        } else if (frames_read < 0) {
            print_error("snd_pcm_readi", frames_read);
            if (snd_pcm_recover(capture_handle, frames_read, 0) < 0) {
                break;
            }
            continue;
        }
        
        // 简单的降采样和格式转换
        snd_pcm_uframes_t output_frames = frames_read / downsample_ratio;
        
        for (snd_pcm_uframes_t i = 0; i < output_frames; i++) {
            // 降采样：每N个样本取1个
            int32_t left_32 = capture_buffer[(i * downsample_ratio) * 2];
            int32_t right_32 = capture_buffer[(i * downsample_ratio) * 2 + 1];
            
            // 从32位转换到16位，应用增益
            float gain = 100.0f;  // 大增益
            
            // 转换并限制范围
            float left_float = (left_32 >> 16) * gain;
            float right_float = (right_32 >> 16) * gain;
            
            if (left_float > 32767.0f) left_float = 32767.0f;
            if (left_float < -32768.0f) left_float = -32768.0f;
            if (right_float > 32767.0f) right_float = 32767.0f;
            if (right_float < -32768.0f) right_float = -32768.0f;
            
            playback_buffer[i * 2] = (int16_t)left_float;
            playback_buffer[i * 2 + 1] = (int16_t)right_float;
        }
        
        // 写入数据
        snd_pcm_sframes_t frames_written = snd_pcm_writei(playback_handle, playback_buffer, output_frames);
        
        if (frames_written == -EPIPE) {
            printf("Playback underrun, recovering...\n");
            snd_pcm_prepare(playback_handle);
            continue;
        } else if (frames_written < 0) {
            print_error("snd_pcm_writei", frames_written);
            if (snd_pcm_recover(playback_handle, frames_written, 0) < 0) {
                break;
            }
            continue;
        }
        
        frame_count++;
        if (frame_count % 100 == 0) {
            // 计算音频电平
            int32_t max_input = 0;
            int32_t sum_input = 0;
            for (snd_pcm_uframes_t i = 0; i < frames_read * 2; i++) {
                int32_t val = abs(capture_buffer[i]);
                if (val > max_input) max_input = val;
                sum_input += val;
            }

            int16_t max_output = 0;
            for (snd_pcm_uframes_t i = 0; i < output_frames * 2; i++) {
                int16_t val = abs(playback_buffer[i]);
                if (val > max_output) max_output = val;
            }

            // 显示原始32位值和平均值
            int32_t avg_input = sum_input / (frames_read * 2);
            printf("Frame %d: Read=%ld, Written=%ld, Input_max=%d(raw=%d), avg=%d, Output_max=%d\n",
                   frame_count, frames_read, frames_written, max_input >> 16, max_input, avg_input, max_output);

            // 如果有输入信号，显示前几个样本
            if (max_input > 0) {
                printf("  First samples: %d, %d, %d, %d\n",
                       capture_buffer[0], capture_buffer[1], capture_buffer[2], capture_buffer[3]);
            }
        }
    }
    
cleanup:
    // 清理
    if (capture_buffer) free(capture_buffer);
    if (playback_buffer) free(playback_buffer);
    
    if (capture_handle) {
        snd_pcm_drop(capture_handle);
        snd_pcm_close(capture_handle);
    }
    
    if (playback_handle) {
        snd_pcm_drop(playback_handle);
        snd_pcm_close(playback_handle);
    }
    
    printf("Audio loopback finished\n");
    return 0;
}

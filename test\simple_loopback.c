#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <alsa/asoundlib.h>
#include <signal.h>
#include <unistd.h>

// 全局变量
static volatile int g_running = 1;

// 信号处理函数
void signal_handler(int sig) {
    printf("\nReceived signal %d, stopping...\n", sig);
    g_running = 0;
}

// 打印错误信息
void print_error(const char *func, int err) {
    fprintf(stderr, "ALSA error in %s: %s\n", func, snd_strerror(err));
}

// 打印设备信息
void print_device_info(snd_pcm_t *handle, const char *name) {
    snd_pcm_hw_params_t *params;
    snd_pcm_hw_params_alloca(&params);
    
    printf("=== Device Info: %s ===\n", name);
    
    if (snd_pcm_hw_params_any(handle, params) < 0) {
        printf("Cannot get hardware parameters\n");
        return;
    }
    
    // 获取声道数范围
    unsigned int min_channels, max_channels;
    snd_pcm_hw_params_get_channels_min(params, &min_channels);
    snd_pcm_hw_params_get_channels_max(params, &max_channels);
    printf("Channels: %u-%u\n", min_channels, max_channels);
    
    // 获取采样率范围
    unsigned int min_rate, max_rate;
    int dir;
    snd_pcm_hw_params_get_rate_min(params, &min_rate, &dir);
    snd_pcm_hw_params_get_rate_max(params, &max_rate, &dir);
    printf("Sample rates: %u-%u Hz\n", min_rate, max_rate);
    
    // 获取格式
    printf("Formats: ");
    snd_pcm_format_t format;
    for (format = 0; format <= SND_PCM_FORMAT_LAST; format++) {
        if (snd_pcm_hw_params_test_format(handle, params, format) == 0) {
            printf("%s ", snd_pcm_format_name(format));
        }
    }
    printf("\n");
    
    printf("======================\n");
}

// 查找USB麦克风设备
int find_usb_mic(char *device_name, size_t size) {
    snd_ctl_t *handle;
    snd_ctl_card_info_t *info;
    snd_pcm_info_t *pcminfo;
    int card = -1;
    
    snd_ctl_card_info_alloca(&info);
    snd_pcm_info_alloca(&pcminfo);
    
    while (snd_card_next(&card) >= 0 && card >= 0) {
        char name[32];
        snprintf(name, sizeof(name), "hw:%d", card);
        
        if (snd_ctl_open(&handle, name, 0) < 0) continue;
        if (snd_ctl_card_info(handle, info) < 0) {
            snd_ctl_close(handle);
            continue;
        }
        
        const char *card_name = snd_ctl_card_info_get_name(info);
        printf("Found card %d: %s\n", card, card_name);
        
        if (strstr(card_name, "USB") || strstr(card_name, "Audio") || card == 2) {
            int dev = -1;
            while (snd_ctl_pcm_next_device(handle, &dev) >= 0 && dev >= 0) {
                snd_pcm_info_set_device(pcminfo, dev);
                snd_pcm_info_set_subdevice(pcminfo, 0);
                snd_pcm_info_set_stream(pcminfo, SND_PCM_STREAM_CAPTURE);
                
                if (snd_ctl_pcm_info(handle, pcminfo) >= 0) {
                    snprintf(device_name, size, "hw:%d,%d", card, dev);
                    printf("Found USB capture device: %s (%s)\n", device_name, card_name);
                    snd_ctl_close(handle);
                    return 0;
                }
            }
            
            // 如果没找到特定设备，使用卡本身
            snprintf(device_name, size, "hw:%d", card);
            printf("Using USB card device: %s (%s)\n", device_name, card_name);
            snd_ctl_close(handle);
            return 0;
        }
        
        snd_ctl_close(handle);
    }
    
    // 如果没找到，使用默认设备
    snprintf(device_name, size, "default");
    printf("No USB device found, using default\n");
    return 0;
}

// 主函数
int main(int argc, char *argv[]) {
    int err;
    char capture_device[256] = "default";
    char playback_device[256] = "default";
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    printf("=== Ultra Simple Audio Loopback ===\n");
    
    // 查找USB麦克风
    find_usb_mic(capture_device, sizeof(capture_device));
    
    // 打开录音设备
    snd_pcm_t *capture_handle;
    if ((err = snd_pcm_open(&capture_handle, capture_device, SND_PCM_STREAM_CAPTURE, 0)) < 0) {
        print_error("snd_pcm_open capture", err);
        return 1;
    }
    
    // 打开播放设备
    snd_pcm_t *playback_handle;
    if ((err = snd_pcm_open(&playback_handle, playback_device, SND_PCM_STREAM_PLAYBACK, 0)) < 0) {
        print_error("snd_pcm_open playback", err);
        snd_pcm_close(capture_handle);
        return 1;
    }
    
    // 打印设备信息
    print_device_info(capture_handle, capture_device);
    print_device_info(playback_handle, playback_device);
    
    // 配置录音设备
    snd_pcm_hw_params_t *capture_params;
    snd_pcm_hw_params_alloca(&capture_params);
    
    if ((err = snd_pcm_hw_params_any(capture_handle, capture_params)) < 0) {
        print_error("snd_pcm_hw_params_any capture", err);
        goto cleanup;
    }
    
    // 设置访问模式
    if ((err = snd_pcm_hw_params_set_access(capture_handle, capture_params, 
                                           SND_PCM_ACCESS_RW_INTERLEAVED)) < 0) {
        print_error("snd_pcm_hw_params_set_access capture", err);
        goto cleanup;
    }
    
    // 设置格式 - 使用S32_LE (USB麦克风支持)
    if ((err = snd_pcm_hw_params_set_format(capture_handle, capture_params, 
                                           SND_PCM_FORMAT_S32_LE)) < 0) {
        print_error("snd_pcm_hw_params_set_format capture", err);
        goto cleanup;
    }
    
    // 设置声道数 - 使用2声道 (USB麦克风支持)
    if ((err = snd_pcm_hw_params_set_channels(capture_handle, capture_params, 2)) < 0) {
        print_error("snd_pcm_hw_params_set_channels capture", err);
        goto cleanup;
    }
    
    // 设置采样率 - 使用96000 (USB麦克风支持)
    unsigned int capture_rate = 96000;
    if ((err = snd_pcm_hw_params_set_rate_near(capture_handle, capture_params, 
                                              &capture_rate, 0)) < 0) {
        print_error("snd_pcm_hw_params_set_rate_near capture", err);
        goto cleanup;
    }
    
    printf("Capture using rate: %u Hz\n", capture_rate);
    
    // 设置周期和缓冲区大小
    snd_pcm_uframes_t capture_period_size = 2048;  // 增加周期大小
    if ((err = snd_pcm_hw_params_set_period_size_near(capture_handle, capture_params,
                                                     &capture_period_size, 0)) < 0) {
        print_error("snd_pcm_hw_params_set_period_size_near capture", err);
        goto cleanup;
    }

    snd_pcm_uframes_t capture_buffer_size = capture_period_size * 16;  // 增加缓冲区大小
    if ((err = snd_pcm_hw_params_set_buffer_size_near(capture_handle, capture_params,
                                                     &capture_buffer_size)) < 0) {
        print_error("snd_pcm_hw_params_set_buffer_size_near capture", err);
        goto cleanup;
    }
    
    // 应用参数
    if ((err = snd_pcm_hw_params(capture_handle, capture_params)) < 0) {
        print_error("snd_pcm_hw_params capture", err);
        goto cleanup;
    }
    
    printf("Capture configured: format=S32_LE, channels=2, rate=%u, period=%lu, buffer=%lu\n", 
           capture_rate, capture_period_size, capture_buffer_size);
    
    // 配置播放设备
    snd_pcm_hw_params_t *playback_params;
    snd_pcm_hw_params_alloca(&playback_params);
    
    if ((err = snd_pcm_hw_params_any(playback_handle, playback_params)) < 0) {
        print_error("snd_pcm_hw_params_any playback", err);
        goto cleanup;
    }
    
    // 设置访问模式
    if ((err = snd_pcm_hw_params_set_access(playback_handle, playback_params, 
                                           SND_PCM_ACCESS_RW_INTERLEAVED)) < 0) {
        print_error("snd_pcm_hw_params_set_access playback", err);
        goto cleanup;
    }
    
    // 设置格式 - 使用S16_LE (更常见的播放格式)
    if ((err = snd_pcm_hw_params_set_format(playback_handle, playback_params, 
                                           SND_PCM_FORMAT_S16_LE)) < 0) {
        print_error("snd_pcm_hw_params_set_format playback", err);
        goto cleanup;
    }
    
    // 设置声道数 - 使用2声道
    if ((err = snd_pcm_hw_params_set_channels(playback_handle, playback_params, 2)) < 0) {
        print_error("snd_pcm_hw_params_set_channels playback", err);
        goto cleanup;
    }
    
    // 设置采样率 - 使用48000 (常见的播放采样率)
    unsigned int playback_rate = 48000;
    if ((err = snd_pcm_hw_params_set_rate_near(playback_handle, playback_params, 
                                              &playback_rate, 0)) < 0) {
        print_error("snd_pcm_hw_params_set_rate_near playback", err);
        goto cleanup;
    }
    
    printf("Playback using rate: %u Hz\n", playback_rate);
    
    // 设置周期和缓冲区大小
    snd_pcm_uframes_t playback_period_size = 1024;  // 保持播放周期大小
    if ((err = snd_pcm_hw_params_set_period_size_near(playback_handle, playback_params,
                                                     &playback_period_size, 0)) < 0) {
        print_error("snd_pcm_hw_params_set_period_size_near playback", err);
        goto cleanup;
    }

    snd_pcm_uframes_t playback_buffer_size = playback_period_size * 16;  // 增加缓冲区大小
    if ((err = snd_pcm_hw_params_set_buffer_size_near(playback_handle, playback_params,
                                                     &playback_buffer_size)) < 0) {
        print_error("snd_pcm_hw_params_set_buffer_size_near playback", err);
        goto cleanup;
    }
    
    // 应用参数
    if ((err = snd_pcm_hw_params(playback_handle, playback_params)) < 0) {
        print_error("snd_pcm_hw_params playback", err);
        goto cleanup;
    }
    
    printf("Playback configured: format=S16_LE, channels=2, rate=%u, period=%lu, buffer=%lu\n", 
           playback_rate, playback_period_size, playback_buffer_size);
    
    // 准备设备
    if ((err = snd_pcm_prepare(capture_handle)) < 0) {
        print_error("snd_pcm_prepare capture", err);
        goto cleanup;
    }
    
    if ((err = snd_pcm_prepare(playback_handle)) < 0) {
        print_error("snd_pcm_prepare playback", err);
        goto cleanup;
    }
    
    // 计算降采样比例
    int downsample_ratio = capture_rate / playback_rate;
    printf("Downsample ratio: %d:1 (%u Hz -> %u Hz)\n", 
           downsample_ratio, capture_rate, playback_rate);
    
    // 分配缓冲区
    int32_t *capture_buffer = malloc(capture_period_size * 2 * sizeof(int32_t));
    int16_t *playback_buffer = malloc(playback_period_size * 2 * sizeof(int16_t));
    
    if (!capture_buffer || !playback_buffer) {
        printf("Failed to allocate buffers\n");
        goto cleanup;
    }
    
    printf("Starting audio loopback...\n");
    printf("Press Ctrl+C to stop\n");
    
    // 启动录音
    if ((err = snd_pcm_start(capture_handle)) < 0) {
        print_error("snd_pcm_start", err);
        goto cleanup;
    }
    
    // 主循环
    int frames = 0;
    while (g_running) {
        // 读取数据
        err = snd_pcm_readi(capture_handle, capture_buffer, capture_period_size);
        
        if (err == -EPIPE) {
            printf("Capture underrun\n");
            snd_pcm_prepare(capture_handle);
            continue;
        } else if (err < 0) {
            print_error("snd_pcm_readi", err);
            if (snd_pcm_recover(capture_handle, err, 0) < 0) {
                break;
            }
            continue;
        }
        
        // 简单的降采样和格式转换 (96kHz 32位 -> 48kHz 16位)
        int output_samples = err / downsample_ratio;
        
        for (int i = 0; i < output_samples; i++) {
            // 改进的降采样：使用平均值
            int64_t left_sum = 0, right_sum = 0;

            for (int j = 0; j < downsample_ratio; j++) {
                int idx = (i * downsample_ratio + j) * 2;
                if (idx < err * 2) {
                    left_sum += capture_buffer[idx];
                    right_sum += capture_buffer[idx + 1];
                }
            }

            int32_t left = left_sum / downsample_ratio;
            int32_t right = right_sum / downsample_ratio;
            
            // 从32位转换到16位，并应用大增益
            float gain = 50.0f;  // 大幅增加增益

            // 先转换到16位
            int16_t left_raw = (int16_t)(left >> 16);
            int16_t right_raw = (int16_t)(right >> 16);

            // 应用增益
            float left_float = left_raw * gain;
            float right_float = right_raw * gain;

            // 限制范围
            if (left_float > 32767.0f) left_float = 32767.0f;
            if (left_float < -32768.0f) left_float = -32768.0f;
            if (right_float > 32767.0f) right_float = 32767.0f;
            if (right_float < -32768.0f) right_float = -32768.0f;

            int16_t left_16 = (int16_t)left_float;
            int16_t right_16 = (int16_t)right_float;

            // 降低噪声门控阈值
            if (abs(left_16) < 50) left_16 = 0;
            if (abs(right_16) < 50) right_16 = 0;
            
            playback_buffer[i * 2] = left_16;
            playback_buffer[i * 2 + 1] = right_16;
        }
        
        // 写入数据
        err = snd_pcm_writei(playback_handle, playback_buffer, output_samples);
        
        if (err == -EPIPE) {
            printf("Playback underrun\n");
            snd_pcm_prepare(playback_handle);
            continue;
        } else if (err < 0) {
            print_error("snd_pcm_writei", err);
            if (snd_pcm_recover(playback_handle, err, 0) < 0) {
                break;
            }
            continue;
        }
        
        frames++;
        if (frames % 100 == 0) {
            // 计算音频电平
            int32_t max_input = 0;
            for (int i = 0; i < capture_period_size * 2; i++) {
                int32_t val = abs(capture_buffer[i]);
                if (val > max_input) max_input = val;
            }
            
            int16_t max_output = 0;
            for (int i = 0; i < output_samples * 2; i++) {
                int16_t val = abs(playback_buffer[i]);
                if (val > max_output) max_output = val;
            }
            
            printf("Frame %d: Input max=%d, Output max=%d\n", 
                   frames, max_input >> 16, max_output);
        }
    }
    
cleanup:
    // 清理
    if (capture_buffer) free(capture_buffer);
    if (playback_buffer) free(playback_buffer);
    
    if (capture_handle) {
        snd_pcm_drop(capture_handle);
        snd_pcm_close(capture_handle);
    }
    
    if (playback_handle) {
        snd_pcm_drop(playback_handle);
        snd_pcm_close(playback_handle);
    }
    
    printf("Audio loopback test finished\n");
    return 0;
}

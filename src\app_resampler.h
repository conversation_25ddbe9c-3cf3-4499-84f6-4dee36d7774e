#ifndef APP_RESAMPLER_H
#define APP_RESAMPLER_H

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <stdbool.h>
#include <speex/speex_resampler.h>

// 应用重采样器配置
typedef struct {
    SpeexResamplerState *resampler;
    int input_rate;
    int output_rate;
    int channels;
    int quality;  // 0-10, 10为最高质量
    bool initialized;
} AppResamplerWrapper;

/**
 * 初始化应用重采样器
 * @param wrapper 重采样器包装结构
 * @param input_rate 输入采样率
 * @param output_rate 输出采样率
 * @param channels 声道数
 * @param quality 质量等级 (0-10)
 * @return 0成功，-1失败
 */
int app_resampler_wrapper_init(AppResamplerWrapper *wrapper,
                              int input_rate, int output_rate,
                              int channels, int quality);

/**
 * 执行重采样
 * @param wrapper 重采样器包装结构
 * @param input 输入音频数据
 * @param input_len 输入样本数（每声道）
 * @param output 输出音频数据缓冲区
 * @param output_len 输出缓冲区大小（每声道）
 * @param actual_output_len 实际输出样本数（每声道）
 * @return 0成功，-1失败
 */
int app_resampler_wrapper_process(AppResamplerWrapper *wrapper,
                                 const int16_t *input, int input_len,
                                 int16_t *output, int output_len,
                                 int *actual_output_len);

/**
 * 清理重采样器
 * @param wrapper 重采样器包装结构
 */
void app_resampler_wrapper_cleanup(AppResamplerWrapper *wrapper);

/**
 * 计算输出样本数
 * @param wrapper 重采样器包装结构
 * @param input_len 输入样本数
 * @return 预期输出样本数
 */
int app_resampler_wrapper_get_output_length(const AppResamplerWrapper *wrapper,
                                           int input_len);

/**
 * 重置重采样器状态
 * @param wrapper 重采样器包装结构
 * @return 0成功，-1失败
 */
int app_resampler_wrapper_reset(AppResamplerWrapper *wrapper);

/**
 * 获取重采样器延迟
 * @param wrapper 重采样器包装结构
 * @return 延迟样本数
 */
int app_resampler_wrapper_get_delay(const AppResamplerWrapper *wrapper);

/**
 * 简化的重采样函数（用于替换现有的简单重采样）
 */
int app_resample_audio(const int16_t *input, int input_samples, int input_rate, int input_channels,
                      int16_t *output, int output_buffer_size, int output_rate, int output_channels,
                      int *output_samples);

#endif // APP_RESAMPLER_H

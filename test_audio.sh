#!/bin/bash

# 音频处理测试脚本

set -e

echo "=== 4声道麦克风阵列音频处理测试 ==="
echo

# 检查可执行文件
if [ ! -f "build/mic_dev" ]; then
    echo "错误: 未找到可执行文件 build/mic_dev"
    echo "请先运行 ./build.sh 编译项目"
    exit 1
fi

# 检查输入文件
INPUT_FILE="/home/<USER>/test20s.pcm"
if [ ! -f "$INPUT_FILE" ]; then
    echo "错误: 未找到输入文件 $INPUT_FILE"
    echo "请确认文件路径是否正确"
    exit 1
fi

# 获取文件信息
FILE_SIZE=$(stat -c%s "$INPUT_FILE" 2>/dev/null || echo "unknown")
echo "输入文件: $INPUT_FILE"
echo "文件大小: $FILE_SIZE 字节"

if [ "$FILE_SIZE" != "unknown" ]; then
    # 计算预期的音频时长 (4声道, 16位, 16kHz)
    SAMPLES_PER_CHANNEL=$((FILE_SIZE / 8))  # 8 = 4声道 * 2字节
    DURATION=$(echo "scale=2; $SAMPLES_PER_CHANNEL / 16000" | bc -l 2>/dev/null || echo "unknown")
    echo "预期时长: ${DURATION}秒"
fi

echo

# 创建输出目录
OUTPUT_DIR="output"
mkdir -p "$OUTPUT_DIR"

# 测试1: 基本处理 (无参考信号)
echo "测试1: 基本音频处理 (波束形成 + 噪声抑制 + AGC)"
OUTPUT_FILE1="$OUTPUT_DIR/processed_basic.pcm"

echo "运行命令: ./build/mic_dev $INPUT_FILE $OUTPUT_FILE1"
time ./build/mic_dev "$INPUT_FILE" "$OUTPUT_FILE1"

if [ -f "$OUTPUT_FILE1" ]; then
    OUTPUT_SIZE=$(stat -c%s "$OUTPUT_FILE1" 2>/dev/null || echo "unknown")
    echo "输出文件: $OUTPUT_FILE1"
    echo "输出大小: $OUTPUT_SIZE 字节"
    echo "✓ 基本处理测试完成"
else
    echo "✗ 基本处理测试失败"
    exit 1
fi

echo

# 测试2: 带参考信号处理 (如果有参考文件)
REF_FILE="/home/<USER>/ref.pcm"
if [ -f "$REF_FILE" ]; then
    echo "测试2: 带参考信号的处理 (包含回声消除)"
    OUTPUT_FILE2="$OUTPUT_DIR/processed_with_aec.pcm"
    
    echo "运行命令: ./build/mic_dev $INPUT_FILE $OUTPUT_FILE2 $REF_FILE"
    time ./build/mic_dev "$INPUT_FILE" "$OUTPUT_FILE2" "$REF_FILE"
    
    if [ -f "$OUTPUT_FILE2" ]; then
        OUTPUT_SIZE2=$(stat -c%s "$OUTPUT_FILE2" 2>/dev/null || echo "unknown")
        echo "输出文件: $OUTPUT_FILE2"
        echo "输出大小: $OUTPUT_SIZE2 字节"
        echo "✓ AEC处理测试完成"
    else
        echo "✗ AEC处理测试失败"
    fi
else
    echo "跳过测试2: 未找到参考信号文件 $REF_FILE"
fi

echo

# 生成测试报告
echo "=== 测试报告 ==="
echo "输入文件: $INPUT_FILE ($FILE_SIZE 字节)"
echo "输出目录: $OUTPUT_DIR"
echo

if [ -f "$OUTPUT_FILE1" ]; then
    echo "✓ 基本处理: $OUTPUT_FILE1"
fi

if [ -f "$OUTPUT_FILE2" ]; then
    echo "✓ AEC处理: $OUTPUT_FILE2"
fi

echo
echo "测试完成! 可以使用音频播放器播放输出文件进行质量评估。"
echo

# 提供播放建议
echo "播放建议:"
echo "1. 使用aplay播放 (如果可用):"
echo "   aplay -f S16_LE -r 16000 -c 1 $OUTPUT_FILE1"
echo
echo "2. 使用ffplay播放:"
echo "   ffplay -f s16le -ar 16000 -ac 1 $OUTPUT_FILE1"
echo
echo "3. 转换为WAV格式:"
echo "   ffmpeg -f s16le -ar 16000 -ac 1 -i $OUTPUT_FILE1 output.wav"

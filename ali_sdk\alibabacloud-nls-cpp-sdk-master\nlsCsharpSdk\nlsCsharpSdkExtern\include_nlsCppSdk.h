/*
 * Copyright 2021 Alibaba Group Holding Limited
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _INCLUDE_NLSCPPSDK_H_
#define _INCLUDE_NLSCPPSDK_H_

#include <ctime>
#include <map>
#include <string>
#include <iostream>
#include <vector>
#include <fstream>

#include "nlsGlobal.h"
#include "nlsClient.h"
#include "nlsEvent.h"
#include "nlsToken.h"
#include "FileTrans.h"
#include "speechTranscriberRequest.h"
#include "speechRecognizerRequest.h"
#include "speechSynthesizerRequest.h"

#endif  // _INCLUDE_NLSCPPSDK_H_

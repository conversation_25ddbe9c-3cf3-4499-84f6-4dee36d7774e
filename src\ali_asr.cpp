#include "ali_asr.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include <sys/time.h>
#include <curl/curl.h>
#include <json-c/json.h>

extern "C" {
#include "audio_realtime.h"  // 包含全局重采样器访问函数
}
#include <iostream>
#include <string>

// 阿里云SDK头文件
#include "nlsClient.h"
#include "nlsEvent.h"
#include "speechTranscriberRequest.h"

// 全局ASR管理器
static AliASRManager g_asr_manager = {
    .initialized = false,
    .token_info = {0},
    .nls_client = NULL,
    .transcriber_request = NULL,
    .state = ALI_ASR_DISCONNECTED,
    .sample_rate = 0,
    .channels = 0,
    .callback = NULL,
    .user_data = NULL,
    .state_mutex = PTHREAD_MUTEX_INITIALIZER,
    .state_cond = PTHREAD_COND_INITIALIZER,
    .callback_mutex = PTHREAD_MUTEX_INITIALIZER,
    .running = false,
    .stop_requested = false
};

// HTTP响应结构
typedef struct {
    char *data;
    size_t size;
} HTTPResponse;

// HTTP响应回调
static size_t http_response_callback(void *contents, size_t size, size_t nmemb, HTTPResponse *response) {
    size_t total_size = size * nmemb;
    
    char *new_data = (char*)realloc(response->data, response->size + total_size + 1);
    if (!new_data) {
        printf("❌ Failed to allocate memory for HTTP response\n");
        return 0;
    }
    
    response->data = new_data;
    memcpy(response->data + response->size, contents, total_size);
    response->size += total_size;
    response->data[response->size] = '\0';
    
    return total_size;
}

/**
 * 初始化阿里云ASR模块（应用启动时调用一次）
 */
int ali_asr_init(int sample_rate, int channels, AliASRCallback callback, void *user_data) {
    if (g_asr_manager.initialized) {
        printf("⚠️ Ali ASR already initialized\n");
        return 0;
    }
    
    printf("🚀 Initializing Ali ASR module...\n");
    
    // 初始化CURL
    CURLcode curl_result = curl_global_init(CURL_GLOBAL_DEFAULT);
    if (curl_result != CURLE_OK) {
        printf("❌ Failed to initialize CURL: %s\n", curl_easy_strerror(curl_result));
        return -1;
    }
    
    // 初始化阿里云NLS客户端
    AlibabaNls::NlsClient* client = AlibabaNls::NlsClient::getInstance();
    client->setLogConfig("/tmp/ali_asr_debug", AlibabaNls::LogDebug); // Enable debug logging
    client->startWorkThread(1); // 启动1个工作线程
    // 初始化管理器
    memset(&g_asr_manager, 0, sizeof(AliASRManager));
    g_asr_manager.sample_rate = sample_rate;
    g_asr_manager.channels = channels;
    g_asr_manager.callback = callback;
    g_asr_manager.user_data = user_data;
    g_asr_manager.state = ALI_ASR_DISCONNECTED;
    
    // 初始化互斥锁和条件变量
    if (pthread_mutex_init(&g_asr_manager.state_mutex, NULL) != 0) {
        printf("❌ Failed to initialize state mutex\n");
        curl_global_cleanup();
        return -1;
    }
    
    if (pthread_cond_init(&g_asr_manager.state_cond, NULL) != 0) {
        printf("❌ Failed to initialize state condition\n");
        pthread_mutex_destroy(&g_asr_manager.state_mutex);
        curl_global_cleanup();
        return -1;
    }
    
    if (pthread_mutex_init(&g_asr_manager.callback_mutex, NULL) != 0) {
        printf("❌ Failed to initialize callback mutex\n");
        pthread_cond_destroy(&g_asr_manager.state_cond);
        pthread_mutex_destroy(&g_asr_manager.state_mutex);
        curl_global_cleanup();
        return -1;
    }
    
    // 获取Token
    if (ali_asr_get_token(&g_asr_manager.token_info) != 0) {
        printf("❌ Failed to get Ali token during initialization\n");
        pthread_mutex_destroy(&g_asr_manager.callback_mutex);
        pthread_cond_destroy(&g_asr_manager.state_cond);
        pthread_mutex_destroy(&g_asr_manager.state_mutex);
        curl_global_cleanup();
        return -1;
    }
    
    g_asr_manager.initialized = true;
    printf("✅ Ali ASR module initialized: %dHz, %dch\n", sample_rate, channels);
    printf("🔑 AppKey: %s\n", g_asr_manager.token_info.appKey);
    printf("🎫 Token: %.20s...\n", g_asr_manager.token_info.token);
    
    return 0;
}

/**
 * 清理阿里云ASR模块（应用退出时调用）
 */
void ali_asr_cleanup(void) {
    if (!g_asr_manager.initialized) {
        return;
    }
    
    printf("🧹 Cleaning up Ali ASR module...\n");
    
    // 停止连接
    ali_asr_stop_connection();
    
    // 清理阿里云SDK
    if (g_asr_manager.transcriber_request) {
        AlibabaNls::NlsClient::getInstance()->releaseTranscriberRequest(
            (AlibabaNls::SpeechTranscriberRequest*)g_asr_manager.transcriber_request);
        g_asr_manager.transcriber_request = NULL;
    }
    
    // 清理资源
    pthread_mutex_destroy(&g_asr_manager.callback_mutex);
    pthread_cond_destroy(&g_asr_manager.state_cond);
    pthread_mutex_destroy(&g_asr_manager.state_mutex);
    
    curl_global_cleanup();
    
    memset(&g_asr_manager, 0, sizeof(AliASRManager));
    printf("✅ Ali ASR module cleaned up\n");
}

/**
 * 获取阿里云Token
 */
int ali_asr_get_token(AliTokenInfo *token_info) {
    if (!token_info) {
        printf("❌ Token info is NULL\n");
        return -1;
    }
    
    memset(token_info, 0, sizeof(AliTokenInfo));
    
    CURL *curl = curl_easy_init();
    if (!curl) {
        printf("❌ Failed to initialize CURL for token request\n");
        return -1;
    }
    
    HTTPResponse response = {0};
    
    // 设置CURL选项
    curl_easy_setopt(curl, CURLOPT_URL, ALI_TOKEN_URL);
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, "{}");
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_response_callback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
    
    // 设置HTTP头
    struct curl_slist *headers = NULL;
    headers = curl_slist_append(headers, "Content-Type: application/json");
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    
    printf("🔑 Requesting Ali Token from: %s\n", ALI_TOKEN_URL);
    
    // 执行请求
    CURLcode res = curl_easy_perform(curl);
    long http_code = 0;
    curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);
    
    if (res != CURLE_OK) {
        printf("❌ Token request failed: %s\n", curl_easy_strerror(res));
        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
        if (response.data) free(response.data);
        return -1;
    }
    
    if (http_code != 200) {
        printf("❌ Token request HTTP error: %ld\n", http_code);
        printf("📝 Response: %s\n", response.data ? response.data : "No response");
        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
        if (response.data) free(response.data);
        return -1;
    }
    
    printf("✅ Token request successful\n");
    printf("📝 Response: %s\n", response.data);
    
    // 解析JSON响应
    json_object *root = json_tokener_parse(response.data);
    if (!root) {
        printf("❌ Failed to parse JSON response\n");
        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
        if (response.data) free(response.data);
        return -1;
    }
    
    // 检查code字段
    json_object *code_obj;
    if (json_object_object_get_ex(root, "code", &code_obj)) {
        int code = json_object_get_int(code_obj);
        if (code != 200) {
            printf("❌ Token request failed with code: %d\n", code);
            json_object_put(root);
            curl_slist_free_all(headers);
            curl_easy_cleanup(curl);
            if (response.data) free(response.data);
            return -1;
        }
    }
    
    // 提取data字段
    json_object *data_obj;
    if (!json_object_object_get_ex(root, "data", &data_obj)) {
        printf("❌ No data field in response\n");
        json_object_put(root);
        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
        if (response.data) free(response.data);
        return -1;
    }
    
    // 提取token和appKey
    json_object *token_obj, *appkey_obj;
    if (json_object_object_get_ex(data_obj, "token", &token_obj) &&
        json_object_object_get_ex(data_obj, "appKey", &appkey_obj)) {
        
        const char *token_str = json_object_get_string(token_obj);
        const char *appkey_str = json_object_get_string(appkey_obj);
        
        if (token_str && appkey_str) {
            strncpy(token_info->token, token_str, MAX_TOKEN_SIZE - 1);
            strncpy(token_info->appKey, appkey_str, MAX_APPKEY_SIZE - 1);
            token_info->valid = true;
            
            printf("✅ Token obtained successfully\n");
            printf("🔑 AppKey: %s\n", token_info->appKey);
            printf("🎫 Token: %.20s...\n", token_info->token);
        } else {
            printf("❌ Invalid token or appKey in response\n");
        }
    } else {
        printf("❌ Missing token or appKey in response\n");
    }
    
    // 清理资源
    json_object_put(root);
    curl_slist_free_all(headers);
    curl_easy_cleanup(curl);
    if (response.data) free(response.data);
    
    return token_info->valid ? 0 : -1;
}

/**
 * 设置状态
 */
static void set_state(AliASRState new_state) {
    pthread_mutex_lock(&g_asr_manager.state_mutex);
    g_asr_manager.state = new_state;
    pthread_cond_broadcast(&g_asr_manager.state_cond);
    pthread_mutex_unlock(&g_asr_manager.state_mutex);
}

/**
 * 获取当前状态
 */
AliASRState ali_asr_get_state(void) {
    pthread_mutex_lock(&g_asr_manager.state_mutex);
    AliASRState state = g_asr_manager.state;
    pthread_mutex_unlock(&g_asr_manager.state_mutex);
    return state;
}

/**
 * 检查是否已连接并可以发送音频
 */
bool ali_asr_is_ready(void) {
    AliASRState state = ali_asr_get_state();
    return (state == ALI_ASR_CONNECTED || state == ALI_ASR_STARTED);
}

// 阿里云SDK回调函数
void onTranscriptionStarted(AlibabaNls::NlsEvent* cbEvent, void* cbParam) {
    printf("🚀 Ali ASR: Transcription started, task_id: %s\n", cbEvent->getTaskId());
    printf("📝 Full response: %s\n", cbEvent->getAllResponse());
    set_state(ALI_ASR_STARTED);
}

void onTranscriptionResultChanged(AlibabaNls::NlsEvent* cbEvent, void* cbParam) {
    //printf("🔄 Ali ASR: Intermediate result: %s\n", cbEvent->getResult());

    // 根据新需求，不再回调中间结果
    /*
    if (g_asr_manager.callback) {
        AliASRResult result;
        memset(&result, 0, sizeof(AliASRResult));
        result.success = true;
        result.is_final = false;
        strncpy(result.text, cbEvent->getResult(), sizeof(result.text) - 1);
        strncpy(result.task_id, cbEvent->getTaskId(), sizeof(result.task_id) - 1);
        result.confidence = 0.0; // 中间结果没有置信度

        pthread_mutex_lock(&g_asr_manager.callback_mutex);
        g_asr_manager.callback(&result, g_asr_manager.user_data);
        pthread_mutex_unlock(&g_asr_manager.callback_mutex);
    }
    */
}

void onSentenceEnd(AlibabaNls::NlsEvent* cbEvent, void* cbParam) {
    printf("✅ Ali ASR: Sentence end: %s\n", cbEvent->getResult());
    printf("📝 Full response: %s\n", cbEvent->getAllResponse());

    if (g_asr_manager.callback) {
        AliASRResult result = {0};
        result.success = true;
        result.is_final = true;
        strncpy(result.text, cbEvent->getResult(), sizeof(result.text) - 1);
        strncpy(result.task_id, cbEvent->getTaskId(), sizeof(result.task_id) - 1);
        result.confidence = cbEvent->getSentenceConfidence();
        result.sentence_id = cbEvent->getSentenceIndex();
        result.begin_time = cbEvent->getSentenceBeginTime();
        result.end_time = cbEvent->getSentenceTime();

        pthread_mutex_lock(&g_asr_manager.callback_mutex);
        g_asr_manager.callback(&result, g_asr_manager.user_data);
        pthread_mutex_unlock(&g_asr_manager.callback_mutex);
    }
}

void onTranscriptionCompleted(AlibabaNls::NlsEvent* cbEvent, void* cbParam) {
    printf("✅ Ali ASR: Transcription completed, task_id: %s\n", cbEvent->getTaskId());
}

void onTaskFailed(AlibabaNls::NlsEvent* cbEvent, void* /*cbParam*/) {
    printf("❌ Ali ASR: Task failed, task_id: %s, error: %s\n",
           cbEvent->getTaskId(), cbEvent->getErrorMessage());

    if (g_asr_manager.callback) {
        AliASRResult result;
        memset(&result, 0, sizeof(AliASRResult));
        result.success = false;
        strncpy(result.task_id, cbEvent->getTaskId(), sizeof(result.task_id) - 1);
        strncpy(result.error_msg, cbEvent->getErrorMessage(), sizeof(result.error_msg) - 1);

        pthread_mutex_lock(&g_asr_manager.callback_mutex);
        g_asr_manager.callback(&result, g_asr_manager.user_data);
        pthread_mutex_unlock(&g_asr_manager.callback_mutex);
    }

    set_state(ALI_ASR_ERROR);
}

void onChannelClosed(AlibabaNls::NlsEvent* cbEvent, void* /*cbParam*/) {
    printf("🔌 Ali ASR: Channel closed, task_id: %s\n", cbEvent->getTaskId());
    set_state(ALI_ASR_DISCONNECTED);

    // 标记需要重连
    g_asr_manager.running = false;
    printf("⚠️ Ali ASR connection lost, will need to reconnect\n");
}

/**
 * 启动ASR连接（建立WebSocket连接）
 */
int ali_asr_start_connection(void) {
    if (!g_asr_manager.initialized) {
        printf("❌ Ali ASR not initialized\n");
        return -1;
    }

    if (g_asr_manager.running) {
        printf("⚠️ Ali ASR connection already running\n");
        return 0;
    }

    printf("🚀 Starting Ali ASR connection...\n");

    set_state(ALI_ASR_CONNECTING);

    // 创建实时语音识别请求
    AlibabaNls::SpeechTranscriberRequest* request =
        AlibabaNls::NlsClient::getInstance()->createTranscriberRequest("cpp", true);

    if (!request) {
        printf("❌ Failed to create transcriber request\n");
        set_state(ALI_ASR_ERROR);
        return -1;
    }

    g_asr_manager.transcriber_request = request;

    // 设置回调函数

    // 设置识别启动回调函数
    request->setOnTranscriptionStarted(onTranscriptionStarted, NULL);
    // 设置识别结果变化回调函数
    request->setOnTranscriptionResultChanged(onTranscriptionResultChanged, NULL);
    // 设置一句话结束回调函数
    request->setOnSentenceEnd(onSentenceEnd, NULL);
    // 设置服务端结束服务回调函数。
    request->setOnTranscriptionCompleted(onTranscriptionCompleted, NULL);
    // 设置错误回调函数。
    request->setOnTaskFailed(onTaskFailed, NULL);
    // 设置通道关闭回调函数。
    request->setOnChannelClosed(onChannelClosed, NULL);

    // 设置请求参数 - 按照WebSocket协议规范
    request->setAppKey(g_asr_manager.token_info.appKey);
    request->setToken(g_asr_manager.token_info.token);

    // 音频格式设置 - 使用PCM格式，16kHz采样率
    request->setFormat("pcm");
    request->setSampleRate(16000); // 阿里云ASR支持的采样率

    // 功能设置
    request->setIntermediateResult(true); // 启用中间结果
    request->setPunctuationPrediction(true); // 启用标点预测
    request->setInverseTextNormalization(true); // 启用逆文本标准化

    // 其他参数设置
    request->setMaxSentenceSilence(800); // 最大静音时间800ms
    request->setTimeout(30000); // 连接超时30秒
    request->setRecvTimeout(30000); // 接收超时30秒

    // 启动连接
    int ret = request->start();
    if (ret < 0) {
        printf("❌ Failed to start Ali ASR connection: %d\n", ret);
        AlibabaNls::NlsClient::getInstance()->releaseTranscriberRequest(request);
        g_asr_manager.transcriber_request = NULL;
        set_state(ALI_ASR_ERROR);
        return -1;
    }

    g_asr_manager.running = true;
    set_state(ALI_ASR_CONNECTED);

    printf("✅ Ali ASR connection started, waiting for started event...\n");

    return 0;
}

/**
 * 停止ASR连接
 */
int ali_asr_stop_connection(void) {
    if (!g_asr_manager.running && g_asr_manager.state == ALI_ASR_DISCONNECTED) {
        return 0;
    }

    printf("🛑 Stopping Ali ASR connection...\n");

    g_asr_manager.stop_requested = true;

    if (g_asr_manager.transcriber_request) {
        AlibabaNls::SpeechTranscriberRequest* request =
            (AlibabaNls::SpeechTranscriberRequest*)g_asr_manager.transcriber_request;

        // stop()会向服务端发送结束识别的指令, 服务端返回closed事件
        int ret = request->stop();
        if (ret < 0) {
            printf("❌ stop() failed. ret: %d\n", ret);
        }

        // 等待 onChannelClosed 回调，该回调会将状态设置为 DISCONNECTED
        // 设置一个超时（例如5秒）以防止永久阻塞
        int wait_count = 0;
        while (ali_asr_get_state() != ALI_ASR_DISCONNECTED && wait_count < 500) { // 500 * 10ms = 5s
            usleep(10000); // wait 10ms
            wait_count++;
        }

        if (ali_asr_get_state() != ALI_ASR_DISCONNECTED) {
            printf("⚠️ Timeout waiting for ChannelClosed event.\n");
        }

        // 确认通道关闭后，再安全地释放请求对象
        AlibabaNls::NlsClient::getInstance()->releaseTranscriberRequest(request);
        g_asr_manager.transcriber_request = NULL;
    }

    g_asr_manager.running = false;
    set_state(ALI_ASR_DISCONNECTED);
    printf("✅ Ali ASR connection stopped\n");

    return 0;
}

/**
 * 发送音频数据（实时调用）- 流式处理，按帧发送
 */
int ali_asr_send_audio(const unsigned char *audio_data, size_t data_size) {
    if (!g_asr_manager.initialized) {
        return -1;
    }

    // 检查连接状态，如果断开则尝试重连
    AliASRState state = ali_asr_get_state();
    if (state == ALI_ASR_DISCONNECTED || !g_asr_manager.running) {
        printf("🔄 Ali ASR disconnected, attempting to reconnect...\n");
        if (ali_asr_start_connection() != 0) {
            printf("❌ Failed to reconnect Ali ASR\n");
            return -1;
        }

        // 等待连接就绪 - 等待onTranscriptionStarted回调
        int retry_count = 0;
        while (ali_asr_get_state() != ALI_ASR_STARTED && retry_count < 100) {
            usleep(50000); // 50ms
            retry_count++;
        }

        if (ali_asr_get_state() != ALI_ASR_STARTED) {
            printf("❌ Ali ASR not started after reconnection\n");
            return -1;
        }

        printf("✅ Ali ASR reconnected and started successfully\n");
    }

    if (!g_asr_manager.transcriber_request) {
        printf("❌ Ali ASR transcriber request is NULL\n");
        return -1;
    }

    state = ali_asr_get_state();
    if (state != ALI_ASR_STARTED) {
        // 如果是CONNECTED状态，等待变为STARTED
        if (state == ALI_ASR_CONNECTED) {
            int wait_count = 0;
            while (ali_asr_get_state() == ALI_ASR_CONNECTED && wait_count < 100) {
                usleep(50000); // 50ms
                wait_count++;
            }
            state = ali_asr_get_state();
        }

        if (state != ALI_ASR_STARTED) {
            printf("⚠️ Ali ASR not started yet, state: %d\n", state);
            return -1;
        }
    }

    // 调用者提供的是 16kHz, 1ch PCM 数据，我们直接发送。
    AlibabaNls::SpeechTranscriberRequest* request =
        (AlibabaNls::SpeechTranscriberRequest*)g_asr_manager.transcriber_request;

    int ret = request->sendAudio((uint8_t*)audio_data, data_size);

    if (ret < 0) {
        printf("❌ Failed to send audio data: %d\n", ret);
        return -1;
    }

    // 减少日志输出，只在调试时打印
    static int send_count = 0;
    if (++send_count % 50 == 0) {
        printf("📤 Ali ASR: Sent %d chunks, last chunk size %zu bytes\n", send_count, data_size);
    }

    return 0;
}

/**
 * 打印ASR结果
 */
void ali_asr_result_print(const AliASRResult *result) {
    if (!result) {
        printf("❌ No ASR result to print\n");
        return;
    }

    printf("\n=== Ali ASR Result ===\n");
    printf("Success: %s\n", result->success ? "✅ Yes" : "❌ No");
    printf("Final: %s\n", result->is_final ? "✅ Yes" : "🔄 Intermediate");
    printf("Task ID: %s\n", result->task_id);

    if (result->success && strlen(result->text) > 0) {
        printf("Text: 📝 %s\n", result->text);
        if (result->is_final) {
            printf("Sentence ID: %d\n", result->sentence_id);
            printf("Confidence: %.2f\n", result->confidence);
            printf("Time: %d - %d ms\n", result->begin_time, result->end_time);
        }
    }

    if (!result->success && strlen(result->error_msg) > 0) {
        printf("Error: ❌ %s\n", result->error_msg);
    }
    printf("=====================\n\n");
}

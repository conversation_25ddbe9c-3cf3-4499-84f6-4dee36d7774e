#include "audio_processor.h"
#include <stdio.h>
#include <math.h>
#include <string.h>
#include "logger.h"

/**
 * 初始化波束形成参数
 * @param processor 音频处理器
 * @param mic_spacing 麦克风间距 (米)
 * @param target_angle 目标角度 (弧度，0为正前方)
 * @return 0成功，-1失败
 */
int beamforming_init(AudioProcessor *processor, float mic_spacing, float target_angle) {
    if (!processor) {
        fprintf(stderr, "Error: Invalid processor for beamforming_init");
        return -1;
    }
    
    LOG_INFO(MODULE_BEAMFORMING, "Initializing beamforming...");
    LOG_INFO(MODULE_BEAMFORMING, "Microphone spacing: %.3f m", mic_spacing);
    LOG_INFO(MODULE_BEAMFORMING, "Target angle: %.1f degrees", target_angle * 180.0 / M_PI);
    
    // 计算每个麦克风的延迟样本数
    // 对于线性阵列，麦克风位置为: [0, d]（2声道配置）
    // 其中d为麦克风间距
    
    float sample_period = 1.0f / SAMPLE_RATE;  // 采样周期
    
    for (int i = 0; i < CHANNELS; i++) {
        // 计算第i个麦克风相对于第0个麦克风的位置
        float mic_position = i * mic_spacing;
        
        // 计算声音到达延迟 (相对于第0个麦克风)
        // 对于角度theta，延迟 = (mic_position * sin(theta)) / sound_speed
        float time_delay = (mic_position * sin(target_angle)) / SOUND_SPEED;
        
        // 转换为样本数
        int delay_samples = (int)round(time_delay / sample_period);
        
        // 限制最大延迟
        if (delay_samples > MAX_DELAY_SAMPLES) {
            delay_samples = MAX_DELAY_SAMPLES;
        } else if (delay_samples < -MAX_DELAY_SAMPLES) {
            delay_samples = -MAX_DELAY_SAMPLES;
        }
        
        processor->delay_samples[i] = delay_samples;
        
        LOG_INFO(MODULE_BEAMFORMING, "Mic %d: position=%.3fm, delay=%d samples (%.3fms)", 
               i, mic_position, delay_samples, delay_samples * 1000.0f / SAMPLE_RATE);
    }
    
    // 初始化权重（2声道配置）
    // 对于2声道阵列，给予相等权重
    processor->channel_weights[0] = 0.5f;  // 左声道
    processor->channel_weights[1] = 0.5f;  // 右声道
    
    LOG_INFO(MODULE_BEAMFORMING, "Beamforming initialized successfully");
    return 0;
}

/**
 * 执行Delay-and-Sum波束形成
 * @param processor 音频处理器
 * @param channels 输入的2个声道 [CHANNELS][FRAME_SIZE]
 * @param output 输出的波束形成结果
 * @param samples 样本数
 */
void beamforming_process(AudioProcessor *processor, const int16_t channels[][FRAME_SIZE],
                        float *output, int samples) {
    if (!processor || !channels || !output) {
        fprintf(stderr, "Error: Invalid parameters for beamforming_process");
        return;
    }

    // 清零输出缓冲区
    memset(output, 0, samples * sizeof(float));

    static int debug_counter = 0;
    debug_counter++;
    
    // 对每个输出样本进行处理
    float max_input = 0.0f, max_output = 0.0f;
    int total_valid_channels = 0;

    for (int n = 0; n < samples; n++) {
        float sum = 0.0f;
        int valid_channels = 0;

        // 对每个麦克风声道应用延迟并求和
        for (int ch = 0; ch < CHANNELS; ch++) {
            int delayed_index = n - processor->delay_samples[ch];

            // 检查延迟后的索引是否有效
            if (delayed_index >= 0 && delayed_index < samples) {
                float sample = (float)channels[ch][delayed_index];
                sum += sample * processor->channel_weights[ch];
                valid_channels++;

                // 记录最大输入值
                if (fabs(sample) > max_input) max_input = fabs(sample);
            }
        }

        // 如果有有效的声道，应用增益
        if (valid_channels > 0) {
            output[n] = sum * BEAMFORMING_GAIN;
            total_valid_channels += valid_channels;

            // 记录最大输出值
            if (fabs(output[n]) > max_output) max_output = fabs(output[n]);
        } else {
            output[n] = 0.0f;
        }
    }

    // 每1000帧打印一次调试信息
    if (debug_counter % 1000 == 0) {
        LOG_INFO(MODULE_BEAMFORMING, "🎯 2-Channel Processing: Max input=%.0f, Max output=%.0f, Channels=2/2",
               max_input, max_output);
    }
}

/**
 * 自适应波束形成权重更新 (简化版本)
 * @param processor 音频处理器
 * @param channels 输入声道
 * @param samples 样本数
 */
void adaptive_beamforming_update(AudioProcessor *processor, 
                                const int16_t channels[][FRAME_SIZE], int samples) {
    if (!processor || !channels) {
        return;
    }
    
    // 计算每个声道的功率
    float channel_power[CHANNELS];
    for (int ch = 0; ch < CHANNELS; ch++) {
        channel_power[ch] = 0.0f;
        for (int i = 0; i < samples; i++) {
            float sample = (float)channels[ch][i];
            channel_power[ch] += sample * sample;
        }
        channel_power[ch] /= samples;
    }
    
    // 找到最大功率
    float max_power = channel_power[0];
    for (int ch = 1; ch < CHANNELS; ch++) {
        if (channel_power[ch] > max_power) {
            max_power = channel_power[ch];
        }
    }
    
    // 避免除零
    if (max_power < 1e-6f) {
        return;
    }
    
    // 更新权重 (功率越大权重越大)
    float total_weight = 0.0f;
    for (int ch = 0; ch < CHANNELS; ch++) {
        float normalized_power = channel_power[ch] / max_power;
        processor->channel_weights[ch] = 0.9f * processor->channel_weights[ch] + 
                                       0.1f * normalized_power;
        total_weight += processor->channel_weights[ch];
    }
    
    // 归一化权重
    if (total_weight > 0.0f) {
        for (int ch = 0; ch < CHANNELS; ch++) {
            processor->channel_weights[ch] /= total_weight;
        }
    }
}

/**
 * 计算波束形成的方向性增益
 * @param processor 音频处理器
 * @param angle 角度 (弧度)
 * @return 方向性增益
 */
float calculate_directivity_gain(const AudioProcessor *processor, float angle) {
    if (!processor) {
        return 0.0f;
    }
    
    float gain = 0.0f;
    
    // 计算在给定角度下的阵列响应
    for (int ch = 0; ch < CHANNELS; ch++) {
        float mic_position = ch * MIC_SPACING;
        float phase_shift = 2.0f * M_PI * SAMPLE_RATE * mic_position * sin(angle) / SOUND_SPEED;
        
        // 简化的方向性计算
        float channel_response = cos(phase_shift) * processor->channel_weights[ch];
        gain += channel_response;
    }
    
    return fabs(gain);
}

/**
 * 打印波束形成统计信息
 * @param processor 音频处理器
 */
void print_beamforming_stats(const AudioProcessor *processor) {
    if (!processor) {
        return;
    }

    LOG_INFO(MODULE_BEAMFORMING, "=== 2-Channel Audio Processing Statistics ===");

    LOG_INFO(MODULE_BEAMFORMING, "Processing mode: Stereo microphone array");
    LOG_INFO(MODULE_BEAMFORMING, "Channel configuration: Left + Right microphones");
    LOG_INFO(MODULE_BEAMFORMING, "Processing: Simple mixing with 12x gain");

    // 对于2声道，显示简化的统计信息
    LOG_INFO(MODULE_BEAMFORMING, "Left channel weight: 0.50");
    LOG_INFO(MODULE_BEAMFORMING, "Right channel weight: 0.50");
    LOG_INFO(MODULE_BEAMFORMING, "Output gain: 12.0x (Enhanced)");

    LOG_INFO(MODULE_BEAMFORMING, "Direction detection:");
    LOG_INFO(MODULE_BEAMFORMING, "  LEFT: Left mic energy > Right mic energy");
    LOG_INFO(MODULE_BEAMFORMING, "  RIGHT: Right mic energy > Left mic energy");
    LOG_INFO(MODULE_BEAMFORMING, "  CENTER: Balanced energy");

    LOG_INFO(MODULE_BEAMFORMING, "============================================");
}

/**
 * 设置波束形成目标方向
 * @param processor 音频处理器
 * @param target_angle 目标角度 (弧度)
 * @return 0成功，-1失败
 */
int set_beamforming_direction(AudioProcessor *processor, float target_angle) {
    if (!processor) {
        return -1;
    }
    
    LOG_INFO(MODULE_BEAMFORMING, "Updating beamforming direction to %.1f degrees", 
           target_angle * 180.0f / M_PI);
    
    return beamforming_init(processor, MIC_SPACING, target_angle);
}



/**
 * 48kHz波束形成处理：处理16位交错输入数据，输出16位单声道
 * @param processor 音频处理器
 * @param interleaved_input 16位交错立体声输入数据 [L,R,L,R,...] (48kHz)
 * @param output 16位单声道输出数据 (48kHz)
 * @param frame_size 帧大小（样本数）
 */
void beamforming_process_48khz(AudioProcessor *processor, const int16_t *interleaved_input,
                              int16_t *output, int frame_size) {
    if (!processor || !interleaved_input || !output) {
        LOG_ERROR(MODULE_BEAMFORMING, "❌ Invalid parameters for beamforming_process_48khz");
        return;
    }

    static int debug_counter = 0;
    debug_counter++;

    // 对每个输出样本进行处理
    float max_input = 0.0f, max_output = 0.0f;

    for (int n = 0; n < frame_size; n++) {
        // 提取左右声道数据（16位）
        int16_t left_sample = interleaved_input[n * 2];
        int16_t right_sample = interleaved_input[n * 2 + 1];

        // 记录最大输入值
        if (abs(left_sample) > max_input) max_input = abs(left_sample);
        if (abs(right_sample) > max_input) max_input = abs(right_sample);

        // 波束形成：加权混合两个声道
        // 使用处理器中的权重配置
        float beamformed = (float)left_sample * processor->channel_weights[0] +
                          (float)right_sample * processor->channel_weights[1];

        // 应用波束形成增益
        beamformed *= BEAMFORMING_GAIN;

        // 记录最大输出值
        if (fabs(beamformed) > max_output) max_output = fabs(beamformed);

        // 限制输出范围并转换为16位整数
        if (beamformed > 32767.0f) beamformed = 32767.0f;
        if (beamformed < -32768.0f) beamformed = -32768.0f;

        output[n] = (int16_t)beamformed;
    }

    // 每1000帧打印一次调试信息
    if (debug_counter % 1000 == 0) {
        LOG_INFO(MODULE_BEAMFORMING, "🎯 48kHz Beamforming: Max input=%.0f, Max output=%.0f, Gain=%.1fx",
               max_input, max_output, BEAMFORMING_GAIN);
    }
}

/**
 * 应用窗函数到波束形成输出
 * @param output 波束形成输出
 * @param samples 样本数
 * @param window_type 窗函数类型 (0=矩形, 1=汉宁, 2=汉明)
 */
void apply_beamforming_window(float *output, int samples, int window_type) {
    if (!output || samples <= 0) {
        return;
    }

    for (int i = 0; i < samples; i++) {
        float window_value = 1.0f;

        switch (window_type) {
            case 1:  // 汉宁窗
                window_value = 0.5f * (1.0f - cos(2.0f * M_PI * i / (samples - 1)));
                break;
            case 2:  // 汉明窗
                window_value = 0.54f - 0.46f * cos(2.0f * M_PI * i / (samples - 1));
                break;
            default:  // 矩形窗
                window_value = 1.0f;
                break;
        }

        output[i] *= window_value;
    }
}

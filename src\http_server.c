#include "http_server.h"
#include "logger.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <json-c/json.h>

// 全局变量定义
//volatile int g_aec_reference_delay_ms = 87;  // 默认值
volatile int g_aec_reference_delay_ms = 94;  // 默认值

/**
 * 处理 GET /status 请求
 */
static int handle_get_status(struct MHD_Connection *connection) {
    struct MHD_Response *response;
    char response_str[MAX_RESPONSE_SIZE];
    int ret;

    snprintf(response_str, sizeof(response_str),
             "{\"aec_reference_delay_ms\": %d, \"status\": \"running\"}",
             g_aec_reference_delay_ms);

    response = MHD_create_response_from_buffer(strlen(response_str),
                                              response_str,
                                              MHD_RESPMEM_MUST_COPY);
    if (!response) return MHD_NO;

    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    ret = MHD_queue_response(connection, MHD_HTTP_OK, response);
    MHD_destroy_response(response);

    LOG_INFO(MODULE_AUDIO_REALTIME, "HTTP GET /status - AEC delay: %d ms", g_aec_reference_delay_ms);
    return ret;
}

/**
 * 处理 POST /set_aec_delay 请求
 */
static int handle_post_set_aec_delay(struct MHD_Connection *connection,
                                     const char *upload_data,
                                     size_t upload_data_size) {
    struct MHD_Response *response;
    char response_str[MAX_RESPONSE_SIZE];
    int ret;

    // 解析 JSON 数据
    char *data = strndup(upload_data, upload_data_size);
    json_object *root = json_tokener_parse(data);

    if (root != NULL) {
        json_object *delay_obj;
        if (json_object_object_get_ex(root, "delay_ms", &delay_obj)) {
            int new_delay = json_object_get_int(delay_obj);

            // 验证延时范围 (5-200ms)
            if (new_delay >= 5 && new_delay <= 200) {
                int old_delay = g_aec_reference_delay_ms;
                g_aec_reference_delay_ms = new_delay;

                LOG_INFO(MODULE_AUDIO_REALTIME,
                        "HTTP POST /set_aec_delay - Changed from %d ms to %d ms",
                        old_delay, new_delay);

                snprintf(response_str, sizeof(response_str),
                        "{\"status\": \"success\", \"old_delay_ms\": %d, \"new_delay_ms\": %d}",
                        old_delay, new_delay);
            } else {
                LOG_INFO(MODULE_AUDIO_REALTIME,
                        "HTTP POST /set_aec_delay - Invalid delay: %d ms (range: 5-200)",
                        new_delay);

                snprintf(response_str, sizeof(response_str),
                        "{\"status\": \"error\", \"message\": \"Invalid delay range (5-200ms)\", \"current_delay_ms\": %d}",
                        g_aec_reference_delay_ms);
            }
        } else {
            snprintf(response_str, sizeof(response_str),
                    "{\"status\": \"error\", \"message\": \"Missing delay_ms parameter\"}");
        }
        json_object_put(root);
    } else {
        snprintf(response_str, sizeof(response_str),
                "{\"status\": \"error\", \"message\": \"Invalid JSON format\"}");
    }

    free(data);

    response = MHD_create_response_from_buffer(strlen(response_str),
                                              response_str,
                                              MHD_RESPMEM_MUST_COPY);
    if (!response) return MHD_NO;

    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    ret = MHD_queue_response(connection, MHD_HTTP_OK, response);
    MHD_destroy_response(response);
    return ret;
}

/**
 * 处理 OPTIONS 请求 (CORS预检)
 */
static int handle_options_request(struct MHD_Connection *connection) {
    struct MHD_Response *response;
    int ret;

    response = MHD_create_response_from_buffer(0, "", MHD_RESPMEM_PERSISTENT);
    if (!response) return MHD_NO;

    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    MHD_add_response_header(response, "Access-Control-Allow-Methods", "GET, POST, OPTIONS");
    MHD_add_response_header(response, "Access-Control-Allow-Headers", "Content-Type");
    ret = MHD_queue_response(connection, MHD_HTTP_OK, response);
    MHD_destroy_response(response);
    return ret;
}

/**
 * 处理 404 错误
 */
static int handle_not_found(struct MHD_Connection *connection) {
    struct MHD_Response *response;
    char response_str[MAX_RESPONSE_SIZE];
    int ret;

    snprintf(response_str, sizeof(response_str),
             "{\"status\": \"error\", \"message\": \"Not found\", \"available_endpoints\": [\"/status\", \"/set_aec_delay\"]}");

    response = MHD_create_response_from_buffer(strlen(response_str),
                                              response_str,
                                              MHD_RESPMEM_MUST_COPY);
    if (!response) return MHD_NO;

    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    ret = MHD_queue_response(connection, MHD_HTTP_NOT_FOUND, response);
    MHD_destroy_response(response);
    return ret;
}

/**
 * 处理 HTTP 请求的回调函数 - 主路由分发器
 */
static int request_handler(void *cls, struct MHD_Connection *connection,
                          const char *url, const char *method,
                          const char *version, const char *upload_data,
                          size_t *upload_data_size, void **con_cls) {

    // 处理 GET /status 请求
    if (strcmp(method, "GET") == 0 && strcmp(url, "/status") == 0) {
        return handle_get_status(connection);
    }

    // 处理 POST /set_aec_delay 请求
    if (strcmp(method, "POST") == 0 && strcmp(url, "/set_aec_delay") == 0) {
        if (*con_cls == NULL) {
            // 初始化 POST 数据处理
            *con_cls = malloc(1); // 简单标记
            return MHD_YES; // 继续接收数据
        }

        if (*upload_data_size > 0) {
            int ret = handle_post_set_aec_delay(connection, upload_data, *upload_data_size);
            *upload_data_size = 0; // 数据已处理
            return ret;
        }
        return MHD_YES;
    }

    // 处理 OPTIONS 请求 (CORS预检)
    if (strcmp(method, "OPTIONS") == 0) {
        return handle_options_request(connection);
    }

    // 默认响应：404
    return handle_not_found(connection);
}

/**
 * 初始化HTTP服务器
 */
int http_server_init(HttpServer *server, int port) {
    if (!server) return -1;
    
    memset(server, 0, sizeof(HttpServer));
    server->port = port;
    server->running = 0;
    
    LOG_INFO(MODULE_AUDIO_REALTIME, "HTTP server initialized on port %d", port);
    return 0;
}

/**
 * 启动HTTP服务器
 */
int http_server_start(HttpServer *server) {
    if (!server || server->running) return -1;
    
    server->daemon = MHD_start_daemon(MHD_USE_SELECT_INTERNALLY, 
                                     server->port, 
                                     NULL, NULL,
                                     &request_handler, 
                                     NULL,
                                     MHD_OPTION_END);
    
    if (server->daemon == NULL) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to start HTTP server on port %d", server->port);
        return -1;
    }
    
    server->running = 1;
    LOG_INFO(MODULE_AUDIO_REALTIME, "🌐 HTTP server started on port %d", server->port);
    LOG_INFO(MODULE_AUDIO_REALTIME, "📡 API endpoints:");
    LOG_INFO(MODULE_AUDIO_REALTIME, "   GET  http://localhost:%d/status", server->port);
    LOG_INFO(MODULE_AUDIO_REALTIME, "   POST http://localhost:%d/set_aec_delay", server->port);
    
    return 0;
}

/**
 * 停止HTTP服务器
 */
void http_server_stop(HttpServer *server) {
    if (!server || !server->running) return;
    
    if (server->daemon) {
        MHD_stop_daemon(server->daemon);
        server->daemon = NULL;
    }
    
    server->running = 0;
    LOG_INFO(MODULE_AUDIO_REALTIME, "HTTP server stopped");
}

/**
 * 清理HTTP服务器
 */
void http_server_cleanup(HttpServer *server) {
    if (!server) return;
    
    http_server_stop(server);
    LOG_INFO(MODULE_AUDIO_REALTIME, "HTTP server cleaned up");
}

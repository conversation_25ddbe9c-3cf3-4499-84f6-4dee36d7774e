#include "voice_dialog.h"
#include "logger.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

#ifndef _WIN32
#include <unistd.h>
#endif


/**
 * 创建语音缓冲区
 */
VoiceBuffer *voice_buffer_create(int capacity, int sample_rate, int channels) {
    VoiceBuffer *buffer = malloc(sizeof(VoiceBuffer));
    if (!buffer) return NULL;

    buffer->buffer = malloc(capacity * sizeof(int16_t));
    if (!buffer->buffer) {
        free(buffer);
        return NULL;
    }

    buffer->capacity = capacity;
    buffer->size = 0;
    buffer->sample_rate = sample_rate;
    buffer->channels = channels;

    if (pthread_mutex_init(&buffer->mutex, NULL) != 0) {
        free(buffer->buffer);
        free(buffer);
        return NULL;
    }

    return buffer;
}

/**
 * 销毁语音缓冲区
 */
void voice_buffer_destroy(VoiceBuffer *buffer) {
    if (!buffer) return;

    pthread_mutex_destroy(&buffer->mutex);
    free(buffer->buffer);
    free(buffer);
}

/**
 * 向缓冲区添加音频数据
 */
int voice_buffer_append(VoiceBuffer *buffer, const int16_t *data, int samples) {
    if (!buffer || !data || samples <= 0) return -1;

    pthread_mutex_lock(&buffer->mutex);

    // 检查容量
    if (buffer->size + samples > buffer->capacity) {
        LOG_INFO(MODULE_VOICE_DIALOG, "Warning: Voice buffer overflow, truncating data");
        samples = buffer->capacity - buffer->size;
        if (samples <= 0) {
            pthread_mutex_unlock(&buffer->mutex);
            return -1;
        }
    }

    // 复制数据
    memcpy(buffer->buffer + buffer->size, data, samples * sizeof(int16_t));

    // 调试：跟踪缓冲区数据添加
    static int append_counter = 0;
    static int total_appended = 0;
    append_counter++;
    total_appended += samples;

    if (append_counter % 1000 == 0) {  // 降低打印频率
        LOG_INFO(MODULE_VOICE_DIALOG,
                 "🔍 Buffer append #%d: +%d samples, first 4: [%d,%d,%d,%d], total: %d samples (%.2fs)",
                 append_counter, samples, data[0], data[1], data[2], data[3],
                 buffer->size + samples, (float) (buffer->size + samples) / buffer->sample_rate / buffer->channels);
    }

    buffer->size += samples;

    pthread_mutex_unlock(&buffer->mutex);
    return samples;
}

/**
 * 清空缓冲区
 */
int voice_buffer_clear(VoiceBuffer *buffer) {
    if (!buffer) return -1;

    pthread_mutex_lock(&buffer->mutex);
    buffer->size = 0;
    pthread_mutex_unlock(&buffer->mutex);

    return 0;
}

/**
 * 保存为PCM文件
 */
int voice_buffer_save_pcm(VoiceBuffer *buffer, const char *filename) {
    if (!buffer || !filename) return -1;

    pthread_mutex_lock(&buffer->mutex);

    FILE *file = fopen(filename, "wb");
    if (!file) {
        pthread_mutex_unlock(&buffer->mutex);
        return -1;
    }

    // 调试：打印保存的音频数据样本
    LOG_INFO(MODULE_VOICE_DIALOG, "🔍 Saving buffer: %d samples, first 8: [%d,%d,%d,%d,%d,%d,%d,%d]",
             buffer->size,
             buffer->buffer[0], buffer->buffer[1], buffer->buffer[2], buffer->buffer[3],
             buffer->buffer[4], buffer->buffer[5], buffer->buffer[6], buffer->buffer[7]);

    size_t written = fwrite(buffer->buffer, sizeof(int16_t), buffer->size, file);
    fclose(file);

    pthread_mutex_unlock(&buffer->mutex);

    LOG_INFO(MODULE_VOICE_DIALOG, "Saved voice data to %s: %d samples, %.2f seconds",
             filename, buffer->size,
             (float) buffer->size / buffer->sample_rate / buffer->channels);

    return (written == (size_t) buffer->size) ? 0 : -1;
}

/**
 * 创建语音对话处理器
 */
VoiceDialogProcessor *voice_dialog_create(void) {
    VoiceDialogProcessor *processor = malloc(sizeof(VoiceDialogProcessor));
    if (!processor) return NULL;

    memset(processor, 0, sizeof(VoiceDialogProcessor));
    processor->vad_confirmation_count = 0;

    // 初始化状态
    processor->state = VOICE_STATE_IDLE;
    processor->vad_threshold = VOICE_SILENCE_THRESHOLD;
    processor->max_record_frames = VOICE_MAX_DURATION * VOICE_SAMPLE_RATE / VOICE_FRAME_SIZE;
    //processor->wake_word_enabled = true;
    //strcpy(processor->wake_word, WAKE_WORD_DEFAULT);

    // 创建语音缓冲区（30秒容量）
    int buffer_capacity = VOICE_MAX_DURATION * VOICE_SAMPLE_RATE * VOICE_CHANNELS;
    processor->voice_buffer = voice_buffer_create(buffer_capacity, VOICE_SAMPLE_RATE, VOICE_CHANNELS);

    if (!processor->voice_buffer) {
        free(processor);
        return NULL;
    }

    if (pthread_mutex_init(&processor->state_mutex, NULL) != 0) {
        voice_buffer_destroy(processor->voice_buffer);
        free(processor);
        return NULL;
    }

    return processor;
}

/**
 * 销毁语音对话处理器
 */
void voice_dialog_destroy(VoiceDialogProcessor *processor) {
    if (!processor) return;

    pthread_mutex_destroy(&processor->state_mutex);
    voice_buffer_destroy(processor->voice_buffer);

    free(processor);
}

/**
 * 设置状态
 */
void voice_dialog_set_state(VoiceDialogProcessor *processor, VoiceDialogState state) {
    if (!processor) return;

    pthread_mutex_lock(&processor->state_mutex);

    if (processor->state != state) {
        LOG_INFO(MODULE_VOICE_DIALOG, "Voice Dialog State: %d -> %d", processor->state, state);
        processor->state = state;

        // 状态切换时的处理
        switch (state) {
            case VOICE_STATE_IDLE:
                processor->is_recording = false;
                processor->speech_frames = 0;
                processor->silence_frames = 0;
                voice_buffer_clear(processor->voice_buffer);  // 现在可以安全清理了
                break;

            case VOICE_STATE_LISTENING:
                processor->is_recording = true;
                processor->current_record_frames = 0;
                voice_buffer_clear(processor->voice_buffer);
                // 旧的回调已移除
                break;

            case VOICE_STATE_PROCESSING:
                // 处理状态现在在录音结束时立即处理，这里只是占位
                break;

            default:
                break;
        }
    }

    pthread_mutex_unlock(&processor->state_mutex);
}

/**
 * 获取状态
 */
VoiceDialogState voice_dialog_get_state(VoiceDialogProcessor *processor) {
    if (!processor) return VOICE_STATE_IDLE;

    pthread_mutex_lock(&processor->state_mutex);
    VoiceDialogState state = processor->state;
    pthread_mutex_unlock(&processor->state_mutex);

    return state;
}



/**
 * 处理音频数据
 */
int voice_dialog_process_audio(VoiceDialogProcessor *processor,
                               const int16_t *audio_data, int samples,int sample_rate, int channels ,
                               bool vad_result) {
    if (!processor || !audio_data) return -1;

    VoiceDialogState current_state = voice_dialog_get_state(processor);

    switch (current_state) {
        case VOICE_STATE_IDLE:
            // 在空闲状态，需要连续检测到 VAD_CONFIRMATION_FRAMES 次人声才开始录音
            if (vad_result) {
                processor->vad_confirmation_count++;
                if (processor->vad_confirmation_count >= VAD_CONFIRMATION_FRAMES) {
                    LOG_INFO(MODULE_VOICE_DIALOG, "🎤 VAD confirmed speech! Starting recording...");

                    voice_dialog_set_state(processor, VOICE_STATE_LISTENING);

                    // 立即开始录音，添加当前帧数据
                    voice_buffer_append(processor->voice_buffer, audio_data, samples);
                    processor->current_record_frames = 1;
                    processor->speech_frames = 1;
                    processor->silence_frames = 0;
                    processor->vad_confirmation_count = 0; // 重置计数器

                    // 调用新的回调：onStart
                    if (processor->on_start) {
                        processor->on_start();
                    }
                }
            } else {
                // 如果VAD未检测到人声，则重置计数器
                processor->vad_confirmation_count = 0;
            }
            break;

        case VOICE_STATE_LISTENING:
            // 录音状态 - 持续添加所有音频数据
            // 无论VAD结果如何，都要保存音频数据以确保完整性
            voice_buffer_append(processor->voice_buffer, audio_data, samples);
            processor->current_record_frames++;

            LOG_DEBUG(MODULE_VOICE_DIALOG, "🎙️ LISTENING: frame %d, buffer size %d",
                     processor->current_record_frames, processor->voice_buffer->size);

            // 每10帧处理一次 = 每100ms处理一次
            if (processor->current_record_frames > 0 && processor->current_record_frames % 10 == 0) {
                // 收集最近100ms的音频数据（10帧）
                int frames_to_collect = 10;
                int samples_to_collect = frames_to_collect * VOICE_FRAME_SIZE; // 10 * 160 = 1600 samples

                LOG_DEBUG(MODULE_VOICE_DIALOG, "🔄 Processing frame %d, need %d samples, buffer has %d samples",
                         processor->current_record_frames, samples_to_collect, processor->voice_buffer->size);

                // 检查缓冲区中是否有足够的数据用于提取
                if (processor->voice_buffer->size >= samples_to_collect) {
                    // 从缓冲区的末尾提取最近的数据块
                    int start_sample_offset = processor->voice_buffer->size - samples_to_collect;
                    int16_t *pcm_buf = processor->voice_buffer->buffer + start_sample_offset;
                    int pcm_size = samples_to_collect * sizeof(int16_t); // 1600 * 2 = 3200 bytes

                    LOG_DEBUG(MODULE_VOICE_DIALOG, "📤 Calling on_process_audio: %d bytes, %dHz, %dch",
                             pcm_size, sample_rate, channels);

                    // 调用onProcessAudio回调
                    if (processor->on_process_audio) {
                        processor->on_process_audio((uint8_t *) pcm_buf, pcm_size, sample_rate, channels);
                    } else {
                        LOG_ERROR(MODULE_VOICE_DIALOG, "❌ on_process_audio callback is NULL!");
                    }
                } else {
                    LOG_DEBUG(MODULE_VOICE_DIALOG, "⚠️ Not enough data in buffer: need %d, have %d",
                             samples_to_collect, processor->voice_buffer->size);
                }
            }

            // VAD状态管理 - 用于判断录音结束时机
            if (vad_result) {
                processor->speech_frames++;
                processor->silence_frames = 0;

                // // 调试：显示检测到语音
                // static int speech_debug_counter = 0;
                // if (++speech_debug_counter % 100 == 0) {
                //     LOG_INFO(MODULE_VOICE_DIALOG, "🗣️ Speech detected (frame %d)", processor->current_record_frames);
                // }
            } else {
                processor->silence_frames++;

                // 语音帧数缓慢衰减，但保持一定数量避免过早结束
                if (processor->speech_frames > 0 && processor->silence_frames > 20) {
                    processor->speech_frames = (processor->speech_frames > 2) ? processor->speech_frames - 1 : 0;
                }
            }

            // 检查录音结束条件
            bool should_stop = false;

            // 1. 超过最大录音时长
            if (processor->current_record_frames >= processor->max_record_frames) {
                LOG_INFO(MODULE_VOICE_DIALOG, "📝 Recording stopped: Maximum duration reached (%d frames)",
                         processor->current_record_frames);
                should_stop = true;
            }

            // 2. 连续静音超过5秒
            // (500 frames * 10ms/frame = 5000ms = 5s)
            if (processor->silence_frames > 500) {
                LOG_INFO(MODULE_VOICE_DIALOG,
                         "📝 Recording stopped: 5 seconds of silence detected (total frames: %d, silence frames: %d)",
                         processor->current_record_frames, processor->silence_frames);
                should_stop = true;
            }

            if (should_stop) {
                // 停止录音，准备处理数据
                processor->is_recording = false;
                voice_dialog_set_state(processor, VOICE_STATE_PROCESSING);

                LOG_INFO(MODULE_VOICE_DIALOG, "🎯 Recording completed! Total samples: %d (%.2fs)",
                         processor->voice_buffer->size,
                         (float) processor->voice_buffer->size / processor->voice_buffer->sample_rate /
                         processor->voice_buffer->channels);

                // 关键：调用 on_finish 前，先复制数据，避免竞态条件
                // on_finish 回调现在负责处理数据，并且应该在处理完毕后释放它
                if (processor->on_finish) {
                    // 我们只复制实际录制的数据，而不是整个容量
                    int data_size_bytes = processor->voice_buffer->size * sizeof(int16_t);
                    uint8_t *data_copy = malloc(data_size_bytes);

                    if (data_copy) {
                        pthread_mutex_lock(&processor->voice_buffer->mutex);
                        memcpy(data_copy, processor->voice_buffer->buffer, data_size_bytes);
                        pthread_mutex_unlock(&processor->voice_buffer->mutex);

                        LOG_INFO(MODULE_VOICE_DIALOG, "✅ Copied %d bytes for on_finish callback.", data_size_bytes);

                        // 使用数据副本调用回调
                        processor->on_finish(data_copy, data_size_bytes, sample_rate, channels);
                    } else {
                        LOG_INFO(MODULE_VOICE_DIALOG, "❌ Failed to allocate memory for data copy. on_finish not called.");
                    }
                }

                // 调用 onStop 回调
                if (processor->on_stop) {
                    processor->on_stop();
                }

                // 清空原始缓冲区并重置状态，为下一次录音做准备
                voice_buffer_clear(processor->voice_buffer);
                voice_dialog_set_state(processor, VOICE_STATE_IDLE);
                processor->current_record_frames = 0;
                processor->speech_frames = 0;
                processor->silence_frames = 0;

                LOG_INFO(MODULE_VOICE_DIALOG, "🔄 Recording system reset, ready for next speech.");
            }
            break;

        default:
            break;
    }

    return 0;
}

/**
 * 设置新的回调函数
 */
int voice_dialog_set_new_callbacks(VoiceDialogProcessor *processor,
                                   VoiceDialogReadyCallback on_ready,
                                   VoiceDialogStartCallback on_start,
                                   VoiceDialogProcessAudioCallback on_process_audio,
                                   VoiceDialogFinishCallback on_finish,
                                   VoiceDialogStopCallback on_stop,
                                   VoiceDialogCloseCallback on_close) {
    if (!processor) return -1;

    processor->on_ready = on_ready;
    processor->on_start = on_start;
    processor->on_process_audio = on_process_audio;
    processor->on_finish = on_finish;
    processor->on_stop = on_stop;
    processor->on_close = on_close;

    LOG_INFO(MODULE_VOICE_DIALOG, "✅ Callbacks set: on_process_audio=%p", (void*)on_process_audio);

    return 0;
}
